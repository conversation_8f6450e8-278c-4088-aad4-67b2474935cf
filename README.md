# 抖音项目后端

## 预付管理系统说明

### 核心逻辑
1. **商户的预付**：通过预付记录表（fa_prepaid）中的最后一次变更后金额获取
2. **每天0点5分自动结算**：系统自动计算昨日总汇后金额，从商户预付中扣除，并生成新的预付记录
3. **预付余额**：实时计算 = 商户当前预付 - 当天总汇后

### 汇后计算特点
- 支持同一天多次费率调整的分段计算
- 按时间段使用对应的费率计算汇后金额
- 确保汇后金额准确反映实际费率变化

### 数据表结构
#### 预付记录表（fa_prepaid）
```sql
CREATE TABLE `fa_prepaid` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户ID',
  `before_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前金额',
  `after_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后金额',
  `change_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更金额（正数=增加，负数=减少）',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预付记录表';
```

#### 注意事项
- 商户表（fa_merchant）不再包含 `prepaid` 字段
- 所有预付相关数据都通过预付记录表管理
- 变更类型通过 `change_amount` 的正负值判断，无需单独字段
- 系统自动结算时 `remark` 格式为：`系统自动结算YYYY-MM-DD汇后金额`

## 商户管理接口

### 商户列表接口
- **接口地址**: `/api/merchant/list`
- **请求方式**: GET
- **功能说明**: 获取商户列表，支持分页和筛选条件
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
  - `name`: 商户名称（模糊搜索）
  - `status`: 状态
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "查询成功",
    "data": {
      "total": 10,
      "list": [
        {
          "id": 1,
          "name": "测试商户",
          "key": "xxx",
          "status": 1,
          "remark": "",
          "prepaid_balance": "500.00",
          "current_prepaid": "800.00",
          "today_wechat_amount": "100.00",
          "yesterday_wechat_amount": "200.00",
          "before_yesterday_wechat_amount": "150.00",
          "today_alipay_amount": "50.00",
          "yesterday_alipay_amount": "100.00",
          "before_yesterday_alipay_amount": "75.00"
        }
      ],
      "page": 1,
      "limit": 10
    }
  }
  ```
- **新增字段说明**:
  - `prepaid_balance`: 预付余额（商户当前预付 - 当天总汇后）
  - `current_prepaid`: 商户当前预付（从预付记录表中获取最后一次变更后金额）

### 增加/减少预付接口
- **接口地址**: `/api/merchant/adjustPrepaid`
- **请求方式**: POST
- **功能说明**: 调整商户的预付金额，支持增加和减少操作
- **请求参数**:
  - `merchant_id`: 商户ID（必填，整数）
  - `amount`: 调整金额（必填，浮点数，正数为增加，负数为减少）
  - `remark`: 备注（可选，字符串）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "预付金额增加成功",
    "data": {
      "merchant_id": 1,
      "before_amount": 1000.00,
      "after_amount": 1500.00,
      "change_amount": 500.00,
      "remark": "充值"
    }
  }
  ```
- **功能特性**:
  - 支持增加和减少预付金额
  - 预付金额可以为负数，不做限制
  - 使用数据库事务确保数据一致性
  - 自动记录所有变更历史到预付记录表
  - 变更类型通过change_amount正负值判断（正数=增加，负数=减少）
- **错误处理**:
  - 商户不存在时返回错误提示
  - 调整金额为0时返回错误提示

### 预付结算任务脚本
- **命令名称**: `settlement:daily`
- **执行方式**: 命令行脚本
- **功能说明**: 每日预付自动结算（自动结算昨日数据）

#### 使用方法

**执行一次结算**（自动结算昨日）：
```bash
php think settlement:daily
```

**启动定时模式**（持续运行，每天0点5分自动执行）：
```bash
php think settlement:daily --daemon
# 或简写
php think settlement:daily -d
```

**查看帮助**：
```bash
php think settlement:daily --help
```

#### 输出示例
```
开始执行 2024-01-19 的预付结算...
商户 测试商户A(ID:1) 结算成功，汇后金额：1250.50
商户 测试商户B(ID:2) 结算成功，汇后金额：850.30
商户 测试商户C(ID:3) 2024-01-19 无汇后金额，跳过

=== 结算完成 ===
结算日期：2024-01-19
总商户数：5
成功结算商户数：2
失败商户数：0
总结算金额：2100.80
```

#### 功能特性
- 自动计算所有商户指定日期的总汇后金额
- 从商户当前预付中扣除汇后金额
- 生成预付记录，记录结算过程（change_amount为负数）
- 支持批量处理所有活跃商户
- 防重复结算（通过remark备注检查已结算记录）
- 支持事务回滚，确保数据一致性
- 详细的执行日志输出

#### 定时任务配置

**方法一：内置定时模式（推荐）**
```bash
# 启动定时模式，自动在每天0点5分执行结算
php think settlement:daily --daemon

# 后台运行定时模式
nohup php think settlement:daily --daemon > /var/log/settlement_daemon.log 2>&1 &

# 查看定时进程状态
ps aux | grep "settlement:daily"
```

**方法二：系统定时任务**
```bash
# 每天0点5分执行结算
0 5 * * * cd /path/to/your/project && php think settlement:daily

# 记录执行日志
0 5 * * * cd /path/to/your/project && php think settlement:daily >> /var/log/settlement.log 2>&1
```

**方法三：Windows任务计划程序**
- 程序/脚本：`php`
- 添加参数：`think settlement:daily`
- 起始于：项目根目录路径
- 触发器：每天0点5分执行

**检查命令是否可用**：
```bash
php think settlement:daily --help
```

**Windows任务计划程序**：
- 程序/脚本：`php`
- 添加参数：`think settlement:daily`
- 起始于：项目根目录路径

#### 手动测试
**测试命令是否正常**：
```bash
# 查看帮助
php think settlement:daily --help

# 执行一次结算（自动结算昨日数据）
php think settlement:daily

# 启动定时模式（按Ctrl+C退出）
php think settlement:daily --daemon

# 查看所有可用命令
php think list
```

#### 注意事项
- 脚本会自动检测重复结算，避免同一天多次扣除
- 使用数据库事务确保数据一致性
- 处理所有商户（包括活跃和非活跃状态）
- 建议在低峰期执行，避免影响业务
- 执行前请确保数据库连接正常
- 脚本会自动结算昨天的数据，无需指定日期
- 定时模式会持续运行，每分钟检查一次时间
- 按 `Ctrl+C` 可以退出定时模式
- 每次执行都会处理一次结算，不会重复执行

### 获取预付记录接口
- **接口地址**: `/api/merchant/getPrepaidRecords`
- **请求方式**: GET
- **功能说明**: 获取指定商户的预付记录历史
- **请求参数**:
  - `merchant_id`: 商户ID（必填，整数）
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "查询预付记录成功",
    "data": {
      "total": 50,
      "list": [
        {
          "id": 1,
          "merchant_id": 1,
          "before_amount": 1000.00,
          "after_amount": 1500.00,
          "change_amount": 500.00,
          "remark": "充值",
          "createtime": 1748520506,
          "createtime_text": "2024-01-15 10:30:06",
          "change_type": "增加"
        }
      ],
      "page": 1,
      "limit": 10,
      "current_prepaid": 1500.00
    }
  }
  ```
- **功能特性**:
  - 按时间倒序排列
  - 自动格式化时间显示
  - 显示变更类型（通过change_amount正负值判断：正数=增加，负数=减少）
  - 返回当前预付余额（从预付记录表获取最后一次变更后金额）

## 预付记录管理接口

### 预付记录列表接口
- **接口地址**: `/api/prepaidrecord/list`
- **请求方式**: GET
- **功能说明**: 获取预付记录列表，支持多种筛选条件
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
  - `merchant_id`: 商户ID（可选）
  - `change_type`: 变更类型（可选，increase/decrease，通过change_amount正负值筛选）
  - `start_time`: 开始时间（可选，格式：Y-m-d）
  - `end_time`: 结束时间（可选，格式：Y-m-d）
  - `remark`: 备注（模糊搜索）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "查询预付记录成功",
    "data": {
      "total": 50,
      "list": [
        {
          "id": 1,
          "merchant_id": 1,
          "merchant_name": "测试商户",
          "before_amount": 1000.00,
          "after_amount": 1500.00,
          "change_amount": 500.00,
          "change_amount_formatted": "500.00",
          "remark": "充值",
          "createtime": 1748520506,
          "createtime_text": "2024-01-15 10:30:06",
          "change_type": "增加"
        }
      ],
      "page": 1,
      "limit": 10
    }
  }
  ```

### 预付记录详情接口
- **接口地址**: `/api/prepaidrecord/detail`
- **请求方式**: GET
- **功能说明**: 获取指定预付记录的详细信息
- **请求参数**:
  - `id`: 记录ID（必填）
- **返回数据**: 包含完整的记录信息和商户名称

### 修改预付记录备注接口
- **接口地址**: `/api/prepaidrecord/updateRemark`
- **请求方式**: POST
- **功能说明**: 修改指定预付记录的备注信息
- **请求参数**:
  - `id`: 记录ID（必填）
  - `remark`: 新的备注内容（可选）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "修改备注成功"
  }
  ```
- **功能特性**:
  - 只修改备注字段，不影响其他数据
  - 自动更新修改时间





## 账号管理接口

### 账号列表接口
- **接口地址**: `/api/account/list`
- **请求方式**: GET
- **功能说明**: 获取账号列表，支持分页和多种筛选条件
- **请求参数**:
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
  - `name`: 账号名称（模糊搜索）
  - `payType`: 支付类型
  - `status`: 状态
  - `remark`: 备注（模糊搜索）
  - `nickname`: 用户昵称（模糊搜索）
  - `hasErrorMsg`: 是否有错误信息（true/false）
  - `minDiamond`: 最小余额（可选，数字类型）
  - `maxDiamond`: 最大余额（可选，数字类型）

    ### 账号导出接口
    - **接口地址**: `/api/account/export`
    - **请求方式**: GET
    - **功能说明**: 导出账号列表为CSV格式文件
    - **请求参数**:
    - `page`: 页码（默认1，导出时忽略）
    - `limit`: 每页数量（默认10，导出时忽略）
    - `name`: 账号名称（模糊搜索）
    - `payType`: 支付类型
    - `status`: 状态
    - `remark`: 备注（模糊搜索）
    - `nickname`: 用户昵称（模糊搜索）
    - `hasErrorMsg`: 是否有错误信息（true/false）
    - `minDiamond`: 最小余额（可选，数字类型）
    - `maxDiamond`: 最大余额（可选，数字类型）
- **返回数据**:
  - `download_url`: 文件下载链接
  - `filename`: 文件名
  - `total_count`: 导出数据总数
- **导出字段**:
  - ID、账号名称、支付类型、状态、钻石数量、权重、错误信息、备注
  - 创建时间、拉取时间、用户昵称
  - 今日订单数、昨日订单数、前日订单数
  - 今日金额、昨日金额、前日金额
- **权限说明**: 
  - 管理员可导出所有账号
  - 普通用户只能导出自己的账号
- **文件格式**: CSV格式，支持中文，自动添加BOM头解决乱码问题
- **文件管理**: 
  - 导出文件保存在 `public/uploads/export/` 目录
  - 自动清理7天前的导出文件
  - 文件名格式：`账号列表_YYYY-MM-DD_HH-mm-ss.csv`
- **错误处理**:
  - 无数据时返回错误提示
  - 文件创建失败时返回详细错误信息
  - 所有错误都会记录到日志文件
- **使用示例**:
  ```bash
  # 导出所有账号
  GET /api/account/export
  
  # 导出指定状态的账号
  GET /api/account/export?status=1
  
  # 导出包含特定关键词的账号
  GET /api/account/export?name=测试&remark=重要
  
  # 导出指定页的账号（page和limit参数会被忽略，导出全部数据）
  GET /api/account/export?page=1&limit=10&status=1
  ```

### 刷新账号余额接口
- **接口地址**: `/api/account/refreshDiamond`
- **请求方式**: POST
- **功能说明**: 刷新指定账号的余额信息
- **请求参数**:
  - `id`: 账号ID（必填）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "刷新成功",
    "data": {
      "diamond": 1000.50
    }
  }
  ```
- **权限说明**: 
  - 管理员可刷新所有账号余额
  - 普通用户只能刷新自己的账号余额
- **功能特性**:
  - 自动调用虎牙API获取最新余额
  - 更新账号的钻石数量和拉取时间
  - 清除账号的错误信息
  - 自动处理登录过期等异常情况
- **错误处理**:
  - 账号不存在时返回错误提示
  - 账号被禁用时返回错误提示
  - 配置信息不完整时返回错误提示
  - 登录过期时自动更新错误信息
  - 网络异常时记录详细错误信息
- **使用示例**:
  ```bash
  # 刷新指定账号余额
  POST /api/account/refreshDiamond
  Content-Type: application/json
  
  {
    "id": 123
  }
  ```

### 设置账号权重接口
- **接口地址**: `/api/account/setWeight`
- **请求方式**: POST
- **功能说明**: 设置指定账号的权重值
- **请求参数**:
  - `id`: 账号ID（必填）
  - `weight`: 权重值（必填，非负整数）
- **返回数据**:
  ```json
  {
    "code": 1,
    "msg": "设置账号权重成功",
    "data": {
      "weight": 10
    }
  }
  ```
- **权限说明**: 
  - 管理员可设置所有账号权重
  - 普通用户只能设置自己的账号权重
- **功能特性**:
  - 权重值用于账号排序，权重高的账号优先显示
  - 权重值必须为非负整数
  - 支持实时修改，立即生效
- **错误处理**:
  - 账号不存在时返回错误提示
  - 权重值为负数时返回错误提示
  - 权限不足时返回错误提示
- **使用示例**:
  ```bash
  # 设置账号权重
  POST /api/account/setWeight
  Content-Type: application/json
  
  {
    "id": 123,
    "weight": 10
  }
  ```

## 定时任务系统使用说明

本项目实现了一个简单的定时任务系统，不依赖数据库，可以方便地执行各种定期任务。

### 定时任务命令

定时任务命令类位于 `application/common/command/Cron.php`，已经预设了一些常用的定时任务：

- 每分钟执行：清理临时缓存
- 每小时执行：处理超时订单
- 每天执行：清理日志文件、生成站点统计报告
- 每周执行：清理过期备份文件
- 每月执行：生成月度报表

### 使用方法

#### 方法一：命令行手动执行

```bash
# 执行所有定时任务
php think cron

# 执行指定任务
php think cron --task=cleanCache
php think cron --task=hourly
php think cron --task=daily
php think cron --task=weekly
php think cron --task=monthly
```

#### 方法二：通过独立脚本执行

项目根目录下的 `cron.php` 是一个独立的脚本文件，可以直接通过PHP执行：

```bash
php cron.php
```

#### 方法三：设置系统定时任务（推荐）

在Linux/Unix系统中，可以通过crontab设置系统定时任务：

```bash
# 编辑crontab
crontab -e

# 添加以下内容（每分钟执行一次）
* * * * * /usr/bin/php /path/to/your/project/cron.php >> /path/to/your/project/runtime/log/cron.log 2>&1
```

在Windows系统中，可以通过计划任务（Task Scheduler）设置定时任务。

### 自定义定时任务

如果需要添加新的定时任务，只需在 `Cron.php` 文件中添加新的任务方法，并在 `runAllTasks()` 方法中调用即可。

例如，添加一个每天中午12点执行的任务：

```php
/**
 * 每天中午执行的任务
 * @param Output $output
 */
protected function taskNoon(Output $output)
{
    $output->writeln("执行任务: 每天中午任务");
    
    // 添加任务逻辑
    
    $output->writeln("每天中午任务执行完成");
}
```

然后在 `runAllTasks()` 方法中添加调用：

```php
// 每天中午12点执行的任务
if ($hour == 12 && $minute == 0) {
    $this->taskNoon($output);
}
```

### 注意事项

1. 定时任务执行时间不要过长，避免多个任务同时执行导致系统负载过高
2. 对于耗时较长的任务，建议使用队列系统处理
3. 定时任务日志保存在 `runtime/log/` 目录下
4. 如果使用系统定时任务，建议设置合理的执行频率，避免过于频繁执行 

# 轮询模式管理功能

## 功能说明
本项目新增了轮询模式管理功能，可以通过API接口动态开启和关闭轮询模式，无需使用数据库，所有配置都保存在配置文件中。

## 配置文件
轮询模式配置保存在 `application/extra/polling.php` 文件中，包含以下配置项：
- `enabled`: 轮询模式开关（true/false）
- `interval`: 轮询间隔时间（秒）
- `timeout`: 轮询超时时间（秒）
- `max_retries`: 最大重试次数

## API接口

### 1. 获取轮询模式状态
- **接口地址**: `GET /api/common/getPollingStatus`
- **功能**: 获取当前轮询模式的配置状态
- **权限**: 需要登录验证
- **返回示例**:
```json
{
    "code": 1,
    "msg": "获取轮询模式状态成功",
    "data": {
        "enabled": false
    }
}
```

### 2. 设置轮询模式
- **接口地址**: `POST /api/common/setPollingMode`
- **功能**: 设置轮询模式的配置参数
- **权限**: 需要登录验证
- **参数**:
  - `enabled` (必填): 是否开启轮询模式（true/false）
- **返回示例**:
```json
{
    "code": 1,
    "msg": "轮询模式已开启",
    "data": {
        "enabled": true
    }
}
```

## 使用示例

### 开启轮询模式
```bash
curl -X POST "http://your-domain/api/common/setPollingMode" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"enabled": true}'
```

### 关闭轮询模式
```bash
curl -X POST "http://your-domain/api/common/setPollingMode" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"enabled": false}'
```

## 注意事项
1. 所有接口都需要登录验证
2. 配置修改后会立即生效，无需重启服务
3. 配置文件会自动备份，修改失败不会影响原有配置
4. 参数验证严格，超出范围的参数会返回错误信息

# 订单数据清理功能

## 功能说明
本项目新增了订单数据清理功能，可以安全地删除指定天数前的历史订单数据，帮助系统维护和数据库优化。

## 安全特性
- **权限控制**: 只有管理员（group_id=1）可以执行删除操作
- **确认机制**: 删除操作需要明确确认参数
- **事务保护**: 使用数据库事务确保操作的原子性
- **操作日志**: 自动记录所有删除操作到管理员日志表

## API接口

### 删除N天前的订单数据
- **接口地址**: `POST /api/common/deleteOldOrders`
- **功能**: 删除指定天数前的订单数据
- **参数**:
  - `days` (必填): 删除多少天前的订单数据
  - `confirm` (必填): 确认删除操作（true/false）
- **权限**: 仅管理员可访问
- **返回示例**:
```json
{
    "code": 1,
    "msg": "成功删除1500条30天前的订单数据",
    "data": {
        "deleted_count": 1500,
        "days": 30,
        "delete_time": "2024-01-01 00:00:00"
    }
}
```

## 使用示例

### 删除30天前的订单数据
```bash
curl -X POST "http://your-domain/api/common/deleteOldOrders" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{
         "days": 30,
         "confirm": true
     }'
```

## 使用建议

### 1. 分批删除
对于大量数据，建议分批删除：
```bash
# 先删除90天前的数据
POST /api/common/deleteOldOrders {"days": 90, "confirm": true}

# 再删除60天前的数据
POST /api/common/deleteOldOrders {"days": 60, "confirm": true}

# 最后删除30天前的数据
POST /api/common/deleteOldOrders {"days": 30, "confirm": true}
```

### 2. 定期清理
建议定期清理历史数据：
- 每周清理90天前的数据
- 每月清理30天前的数据
- 每季度清理7天前的数据

## 注意事项

1. **数据安全**: 删除操作不可逆，请谨慎操作
2. **权限要求**: 只有管理员可以执行删除操作
3. **确认机制**: 必须设置confirm=true才能执行删除
4. **事务保护**: 删除操作使用事务，失败时会自动回滚
5. **日志记录**: 所有删除操作都会记录到管理员日志表
6. **性能考虑**: 大量数据删除时可能耗时较长，建议在低峰期执行
7. **备份建议**: 删除前建议先备份数据库

## 错误处理

### 常见错误及解决方案

1. **权限不足**
   ```
   {"code": 0, "msg": "只有管理员可以执行此操作"}
   ```
   解决方案：使用管理员账号登录

2. **参数错误**
   ```
   {"code": 0, "msg": "天数必须大于0"}
   ```
   解决方案：检查days参数是否正确

3. **未确认删除**
   ```
   {"code": 0, "msg": "请确认删除操作"}
   ```
   解决方案：设置confirm=true

4. **无数据可删除**
   ```
   {"code": 0, "msg": "没有找到符合条件的订单数据"}
   ```
   解决方案：调整days参数或检查数据是否存在

## 技术实现

### 数据库操作
- 使用ThinkPHP的数据库操作API
- 支持事务回滚机制
- 自动记录操作日志

### 安全机制
- 参数验证和过滤
- 权限检查
- SQL注入防护
- 操作确认机制

### 性能优化
- 使用索引优化查询性能
- 批量删除减少数据库压力
- 事务控制确保数据一致性 

# 仪表盘统计相关接口

## 获取最近一小时成功率（5分钟前的一小时）
- **接口地址**: `/api/dashboard/recent_hour_success_rate`
- **请求方式**: GET
- **功能说明**: 获取距离当前时间5分钟前，往前推1小时内的订单成功率（即：统计[当前时间-65分钟, 当前时间-5分钟]区间内的订单成功率）。
- **请求参数**:
  - `paytype` (可选): 支付类型，默认`huya`，可选`yahu`
- **返回数据**:
  - `successRate`: 成功率（百分比，保留两位小数）
  - `totalCount`: 该区间内订单总数
  - `successCount`: 该区间内成功订单数
  - `startTime`: 统计起始时间（格式：Y-m-d H:i:s）
  - `endTime`: 统计结束时间（格式：Y-m-d H:i:s）
- **返回示例**:
```json
{
  "code": 1,
  "msg": "获取最近一小时成功率成功",
  "data": {
    "successRate": 85.71,
    "totalCount": 14,
    "successCount": 12,
    "startTime": "2024-06-01 13:00:00",
    "endTime": "2024-06-01 14:00:00"
  }
}
```

## 获取最近5分钟订单量
- **接口地址**: `/api/dashboard/recent_five_minute_order_count`
- **请求方式**: GET
- **功能说明**: 获取最近5分钟内的订单总数。
- **请求参数**:
  - `paytype` (可选): 支付类型，默认`huya`，可选`yahu`
- **返回数据**:
  - `orderCount`: 订单数量
  - `startTime`: 统计起始时间（格式：Y-m-d H:i:s）
  - `endTime`: 统计结束时间（格式：Y-m-d H:i:s）
- **返回示例**:
```json
{
  "code": 1,
  "msg": "获取最近5分钟订单量成功",
  "data": {
    "orderCount": 3,
    "startTime": "2024-06-01 14:00:00",
    "endTime": "2024-06-01 14:05:00"
  }
}
```

# Telegram机器人功能

## 功能说明
本项目集成了Telegram机器人功能，用于实时推送订单统计数据到指定的Telegram群组，支持定时推送和手动查询。

## 主要功能
- **定时推送**: 自动推送最近一小时、今日、昨日的订单金额统计
- **WebHook处理**: 处理来自Telegram的消息回调，支持命令查询
- **手动推送**: 支持外部系统调用，传入统计数据
- **错误处理**: 完善的异常处理和日志记录

## 配置说明
机器人配置位于 `application/index/controller/Telegram.php` 文件中：
- `$botToken`: Telegram机器人Token
- `$chatId`: 目标聊天群组ID
- `$payTypes`: 支付类型映射配置

## API接口

### 1. 定时推送任务
- **接口地址**: `/index/telegram/messageTask`
- **请求方式**: GET
- **功能说明**: 获取最近一小时、今日、昨日的订单金额统计并推送到Telegram
- **返回数据**: 
  - 成功: `ok`
  - 失败: `error: 错误信息`
- **使用场景**: 定时任务调用，自动推送统计数据

### 2. WebHook处理
- **接口地址**: `/index/telegram/webHook`
- **请求方式**: POST
- **功能说明**: 处理Telegram WebHook消息，支持命令查询
- **支持命令**:
  - `/cx_ts`: 查询统计数据
  - `/id`: 获取聊天ID
  - `/help`: 显示帮助信息

## 消息格式
推送的消息采用HTML格式，包含以下信息：
```
⏰来自定时推送
———2024-01-01 12:00:00———
通道类型：总计金额
小时金额：1,234.56
今日金额：12,345.67
昨日金额：11,234.56
————————————
```

## 错误处理
- **参数验证**: 严格验证输入参数格式
- **异常捕获**: 所有操作都包含异常处理
- **日志记录**: 详细记录操作日志和错误信息
- **API错误**: 检查Telegram API返回结果

## 性能优化
- **代码复用**: 消除重复代码，提高维护性
- **数据库优化**: 使用索引优化查询性能
- **缓存机制**: 避免重复计算统计数据
- **异步处理**: 支持异步消息发送

## 安全特性
- **参数过滤**: 严格验证和过滤输入参数
- **错误隐藏**: 生产环境隐藏敏感错误信息
- **访问控制**: 支持访问权限控制
- **日志审计**: 记录所有操作日志

## 使用建议
1. **定时任务**: 建议每小时执行一次定时推送
2. **监控告警**: 关注推送失败日志，及时处理异常
3. **数据验证**: 定期检查统计数据准确性
4. **性能监控**: 监控接口响应时间和数据库查询性能 