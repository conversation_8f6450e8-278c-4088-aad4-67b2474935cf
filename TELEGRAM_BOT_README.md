# Telegram机器人功能说明

## 功能概述
Telegram机器人已升级，支持多个商户群，每个商户群可以独立查询自己的数据和进行预付管理。

## 数据库修改
在商户表(`fa_merchant`)中新增了`telegram_group_id`字段，用于存储绑定的Telegram群组ID。

## 功能特性

### 1. 系统群功能（管理员群）
- **群组ID**: -1002469029724
- **可用命令**:
  - `/查账` - 查看全系统统计数据（微信、支付宝的小时、今日、昨日金额）
  - `/id` - 显示系统群ID

### 2. 商户群功能
- **群组绑定**: 通过数据库直接设置商户表的`telegram_group_id`字段
- **可用命令**:
  - `/查账` - 查看该商户的统计数据（包含预付余额）
  - `/加预付 金额 [备注]` - 增加预付金额
  - `/减预付 金额 [备注]` - 减少预付金额
  - `/id` - 显示群组和商户信息
  - `/帮助` - 显示帮助信息

## 商户统计数据说明

### 显示内容
- **商户名称**: 绑定的商户名称
- **当前预付**: 商户的当前预付金额
- **预付余额**: 当前预付 - 当天总汇后金额
- **微信通道**: 小时、今日、昨日金额
- **支付宝通道**: 小时、今日、昨日金额
- **总计**: 微信+支付宝的汇总金额

### 预付余额计算
```
预付余额 = 商户当前预付 - 当天总汇后金额
```

## 预付管理功能

### 增加预付
- **命令格式**: `/加预付 金额 [备注]`
- **示例**: `/加预付 1000 充值`
- **功能**: 在商户预付记录表中添加增加记录

### 减少预付
- **命令格式**: `/减预付 金额 [备注]`
- **示例**: `/减预付 500 结算`
- **功能**: 在商户预付记录表中添加减少记录

## 群组绑定方式

### 手动绑定
直接在数据库中设置商户表的`telegram_group_id`字段：
```sql
UPDATE fa_merchant SET telegram_group_id = 群组ID WHERE id = 商户ID;
```

### 群组ID获取
在任意群组中发送`/id`命令即可获取该群组的ID。

## 注意事项

1. **群组唯一性**: 一个群组只能绑定一个商户
2. **权限控制**: 商户群只能操作自己绑定的商户数据
3. **数据安全**: 所有操作都会记录在预付记录表中
4. **实时计算**: 预付余额实时计算，确保数据准确性

## 技术实现

### 核心文件
- `application/index/controller/Telegram.php` - 主控制器
- `application/common/model/Prepaid.php` - 预付模型
- `application/common/model/Merchant.php` - 商户模型

### 数据库表
- `fa_merchant` - 商户表（新增telegram_group_id字段）
- `fa_prepaid` - 预付记录表
- `fa_order` - 订单主表
- `fa_order_history` - 订单归档表

## 使用流程

1. **创建商户群**: 在Telegram中创建商户专用群组
2. **获取群组ID**: 在群组中发送`/id`命令获取ID
3. **绑定商户**: 在数据库中设置商户的telegram_group_id
4. **开始使用**: 商户群即可使用所有功能

## 错误处理

- **未绑定商户**: 显示"该群组未绑定商户，请联系管理员"
- **格式错误**: 显示具体的格式要求和示例
- **操作失败**: 显示具体的错误信息 