<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;
use think\Exception;

/**
 * 商户接口
 */
class Merchant extends Api
{

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['setRate', 'detail', 'adjustPrepaid', 'getPrepaidRecords'];

    /**
     * 获取商户列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status'    => input('status/d', '')
        ];

        $where = [];
        // 构建查询条件
        if ($params['name'] !== '') {
            $where['m.name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['status'] !== '') {
            $where['m.status'] = $params['status'];
        }

        // 查询数据（左连接分组表以获取group_name）
        $list = \think\Db::name('merchant')->alias('m')
            ->join('merchant_group g', 'g.id = m.group_id', 'LEFT')
            ->field('m.id,m.name,m.key,m.status,m.remark,m.min_amount,m.max_amount,m.telegram_group_id,m.group_id,g.name as group_name')
            ->where($where)
            ->order('m.group_id', 'asc')
            ->order('m.id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取前日开始和结束时间戳
        $beforeYesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeYesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 保证$list为数组
        $list = collection($list)->toArray();
        $merchantIds = array_column($list, 'id');
        $paytypes = ['huya', 'yahu'];

        // 批量统计主表
        $mainStats = [];
        if (!empty($merchantIds)) {
            $mainStatsRaw = Db::name('order')
                ->field("merchant_id, paytype,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $beforeYesterdayStart AND $beforeYesterdayEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('merchant_id', 'in', $merchantIds)
                ->where('paytype', 'in', $paytypes)
                ->group('merchant_id,paytype')
                ->select();
            $mainStatsRaw = is_array($mainStatsRaw) ? $mainStatsRaw : $mainStatsRaw->toArray();
            foreach ($mainStatsRaw as $row) {
                $mainStats[$row['merchant_id']][$row['paytype']] = $row;
            }
        }
        // 批量统计归档表
        $archiveStats = [];
        if (!empty($merchantIds)) {
            $archiveStatsRaw = Db::name('order_history')
                ->field("merchant_id, paytype,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $todayStart AND $todayEnd THEN amount ELSE 0 END) as today_amount,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $yesterdayStart AND $yesterdayEnd THEN amount ELSE 0 END) as yesterday_amount,
                    SUM(CASE WHEN callback_status=1 AND callback_time BETWEEN $beforeYesterdayStart AND $beforeYesterdayEnd THEN amount ELSE 0 END) as before_amount
                ")
                ->where('merchant_id', 'in', $merchantIds)
                ->where('paytype', 'in', $paytypes)
                ->group('merchant_id,paytype')
                ->select();
            $archiveStatsRaw = is_array($archiveStatsRaw) ? $archiveStatsRaw : $archiveStatsRaw->toArray();
            foreach ($archiveStatsRaw as $row) {
                $archiveStats[$row['merchant_id']][$row['paytype']] = $row;
            }
        }
        // 合并主表和归档表统计，所有paytype和merchant_id组合都覆盖
        $statsMap = [];
        foreach ($merchantIds as $mid) {
            foreach ($paytypes as $paytype) {
                $main = isset($mainStats[$mid][$paytype]) ? $mainStats[$mid][$paytype] : [
                    'today_amount' => 0,
                    'yesterday_amount' => 0,
                    'before_amount' => 0
                ];
                $archive = isset($archiveStats[$mid][$paytype]) ? $archiveStats[$mid][$paytype] : [
                    'today_amount' => 0,
                    'yesterday_amount' => 0,
                    'before_amount' => 0
                ];
                $statsMap[$mid][$paytype] = [
                    'today_amount' => $main['today_amount'] + $archive['today_amount'],
                    'yesterday_amount' => $main['yesterday_amount'] + $archive['yesterday_amount'],
                    'before_amount' => $main['before_amount'] + $archive['before_amount'],
                ];
            }
        }
        // 为每个商户填充统计数据，保证每个paytype都赋值
        foreach ($list as &$item) {
            $mid = $item['id'];
            foreach ($paytypes as $paytype) {
                $itemKey = $paytype == 'huya' ? 'wechat' : 'alipay';
                $item['today_' . $itemKey . '_amount'] = number_format($statsMap[$mid][$paytype]['today_amount'] ?? 0, 2, '.', '');
                $item['yesterday_' . $itemKey . '_amount'] = number_format($statsMap[$mid][$paytype]['yesterday_amount'] ?? 0, 2, '.', '');
                $item['before_yesterday_' . $itemKey . '_amount'] = number_format($statsMap[$mid][$paytype]['before_amount'] ?? 0, 2, '.', '');
            }

            // 计算预付余额（跑量 - 汇后）
            $item['prepaid_balance'] = $this->calculatePrepaidBalance($mid);

            // 获取当前预付（昨日的预付余额）
            $item['current_prepaid'] = $this->getCurrentPrepaid($mid);
        }

        // 查询总数
        $total = \think\Db::name('merchant')
            ->alias('m')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 添加商户
     */
    public function add()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            if ($merchant->where('key', $params['key'])->find()) {
                throw new \Exception('key已存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 删除商户
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->delete();
        $this->success('删除商户成功');
    }

    /**
     * 修改商户状态
     */
    public function status()
    {
        $id = input('id');
        $status = input('status');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->status = $status;
        $merchant->save();
        $this->success('修改商户状态成功');
    }

    /**
     * 修改商户信息
     */
    public function update()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            $merchant = $merchant->where('id', $params['id'])->find();
            if (!$merchant) {
                throw new \Exception('商户不存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('修改商户信息成功');
    }

    /**
     * 获取商户信息
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }

        // 统计最近30天的数据，按日期分组
        $today = strtotime(date('Y-m-d 23:59:59')); // 今天结束时间
        $thirtyDaysAgo = strtotime(date('Y-m-d 00:00:00', strtotime('-29 days'))); // 30天前开始时间

        // 生成最近30天的日期
        $dates = [];
        $currentDate = $thirtyDaysAgo;
        while ($currentDate <= $today) {
            $dates[] = date('Y-m-d', $currentDate);
            $currentDate = strtotime('+1 day', $currentDate);
        }

        $paytypes = ['huya', 'yahu'];
        $periods = [];
        foreach ($paytypes as $paytype) {
            foreach ($dates as $date) {
                $dayStart = strtotime($date . ' 00:00:00');
                $dayEnd = strtotime($date . ' 23:59:59');
                // 查询当天所有该paytype的费率设置点
                $rateList = \think\Db::name('rate')
                    ->where('merchant_id', $id)
                    ->where('paytype', $paytype)
                    ->where('createtime', 'between', [$dayStart, $dayEnd])
                    ->order('createtime', 'asc')
                    ->select();
                $segments = [];
                $lastTime = $dayStart;
                // 分段点：先收集所有分段点（含0点、所有设置点、23:59:59）
                $splitPoints = [$dayStart];
                foreach ($rateList as $rateRow) {
                    $splitPoints[] = $rateRow['createtime'];
                }
                $splitPoints[] = $dayEnd;
                // 去重并排序
                $splitPoints = array_unique($splitPoints);
                sort($splitPoints);
                // 遍历分段
                for ($i = 0; $i < count($splitPoints) - 1; $i++) {
                    $segStart = $splitPoints[$i];
                    $segEnd = $splitPoints[$i + 1];
                    // 查找分段起始时间点前最近一次设置的费率
                    $rate = \think\Db::name('rate')
                        ->where('merchant_id', $id)
                        ->where('paytype', $paytype)
                        ->where('createtime', '<=', $segStart)
                        ->order('createtime', 'desc')
                        ->value('rate');
                    $rate = $rate !== null ? $rate : 0;
                    // 使用合并主表和归档表的方法统计订单金额
                    $amountWhere = [
                        'merchant_id' => $id,
                        'callback_status' => 1,
                        'callback_time' => ['between', [$segStart, $segEnd]]
                    ];
                    $amount = $this->getOrderStats($amountWhere, $paytype, 'sum', 'amount');
                    // 先查找分段起始时间点前最近一次设置的cost，没有则查找最早一次设置
                    $cost = \think\Db::name('rate')
                        ->where('merchant_id', $id)
                        ->where('paytype', $paytype)
                        ->where('createtime', '<=', $segStart)
                        ->order('createtime', 'desc')
                        ->value('cost');
                    if ($cost === null) {
                        $cost = \think\Db::name('rate')
                            ->where('merchant_id', $id)
                            ->where('paytype', $paytype)
                            ->order('createtime', 'asc')
                            ->value('cost');
                        $cost = $cost !== null ? $cost : 0;
                    }
                    $profit = number_format($amount * (1 - $cost / 100) - ($amount * (1 - $rate / 100)), 2, '.', '');
                    $segments[] = [
                        'start_time' => date('Y-m-d H:i:s', $segStart),
                        'end_time' => date('Y-m-d H:i:s', $segEnd),
                        'amount' => $amount ? number_format($amount, 2, '.', '') : '0.00',
                        'rate' => $rate,
                        'settle_amount' => number_format($amount * (1 - $rate / 100), 2, '.', ''),
                        'cost' => $cost,
                        'profit' => $profit
                    ];
                }
                if (!isset($periods[$paytype])) {
                    $periods[$paytype] = [];
                }
                $periods[$paytype][$date] = $segments;
            }
        }
        $merchant = $merchant->toArray();
        $merchant['rate_periods'] = $periods;
        $this->success('获取商户信息成功', $merchant);
    }

    /**
     * 设置商户费率
     */
    public function setRate()
    {
        $merchant_id = input('id/d');
        $rate = input('rate/f');
        $paytype = input('paytype/s');
        $cost = input('cost/s');
        $createtime = input('time/d', time());
        if (!$merchant_id) {
            $this->error('商户ID不能为空');
        }
        if (!$rate) {
            $this->error('费率不能为空');
        }
        if ($paytype !== 'huya' && $paytype !== 'yahu') {
            $this->error('支付类型错误');
        }
        // 检查商户是否存在
        $merchant = \app\common\model\Merchant::where('id', $merchant_id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $data = [
            'merchant_id' => $merchant_id,
            'rate' => $rate,
            'paytype' => $paytype,
            'createtime' => $createtime
        ];

        // 如果用户没有提交成本，则使用上一次设置的成本
        if (isset($cost) && !empty($cost)) {
            $data['cost'] = $cost;
        } else {
            // 查找该商户该支付类型最近一次设置的成本
            $lastCost = \think\Db::name('rate')
                ->where('merchant_id', $merchant_id)
                ->where('paytype', $paytype)
                ->where('cost', 'not null')
                ->order('createtime', 'desc')
                ->value('cost');

            if ($lastCost !== null) {
                $data['cost'] = $lastCost;
            }
        }
        // 只做新增，不做更新
        $res = \think\Db::name('rate')->insert($data);
        if ($res === false) {
            $this->error('设置费率失败');
        }
        $this->success('设置费率成功');
    }

    /**
     * 增加/减少预付
     */
    public function adjustPrepaid()
    {
        $merchant_id = input('merchant_id/d');
        $amount = input('amount/f'); // 正数为增加，负数为减少
        $remark = input('remark/s', '');

        if (!$merchant_id) {
            $this->error('商户ID不能为空');
        }
        if ($amount == 0) {
            $this->error('调整金额不能为0');
        }

        // 检查商户是否存在
        $merchant = \app\common\model\Merchant::where('id', $merchant_id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }

        Db::startTrans();
        try {
            // 获取当前预付金额（从预付记录表中获取最后一次变更后金额）
            $currentPrepaid = $this->getCurrentPrepaid($merchant_id);
            $newPrepaid = $currentPrepaid + $amount;

            // 预付金额可以为负数，不做限制

            // 保存预付记录（不再更新商户表的prepaid字段）
            $prepaidData = [
                'merchant_id' => $merchant_id,
                'before_amount' => $currentPrepaid,
                'after_amount' => $newPrepaid,
                'change_amount' => $amount,
                'remark' => $remark,
                'createtime' => time(),
                'updatetime' => time()
            ];

            $prepaidRecord = new \app\common\model\Prepaid();
            $prepaidRecord->save($prepaidData);

            Db::commit();

            $action = $amount > 0 ? '增加' : '减少';
            $this->success("预付金额{$action}成功", [
                'merchant_id' => $merchant_id,
                'before_amount' => $currentPrepaid,
                'after_amount' => $newPrepaid,
                'change_amount' => $amount,
                'remark' => $remark
            ]);
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取预付记录列表
     */
    public function getPrepaidRecords()
    {
        $merchant_id = input('merchant_id/d');
        $page = input('page/d', 1);
        $limit = input('limit/d', 10);

        if (!$merchant_id) {
            $this->error('商户ID不能为空');
        }

        // 检查商户是否存在
        $merchant = \app\common\model\Merchant::where('id', $merchant_id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }

        $where = ['merchant_id' => $merchant_id];

        // 查询预付记录
        $list = Db::name('prepaid')
            ->where($where)
            ->order('createtime', 'desc')
            ->page($page, $limit)
            ->select();

        // 格式化时间
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['change_type'] = $item['change_amount'] > 0 ? '增加' : '减少';
        }

        // 查询总数
        $total = Db::name('prepaid')->where($where)->count();

        $data = [
            'total' => $total,
            'list' => $list,
            'page' => $page,
            'limit' => $limit,
            'current_prepaid' => $merchant->prepaid ?? 0
        ];

        $this->success('查询预付记录成功', $data);
    }

    /**
     * 计算预付余额（跑量 - 汇后）
     * @param int $merchant_id 商户ID
     * @return float
     */
    private function calculatePrepaidBalance($merchant_id)
    {
        // 获取商户当前预付（从预付记录表中获取最后一次变更后金额）
        $currentPrepaid = $this->getCurrentPrepaid($merchant_id);

        // 计算当天总汇后
        $todaySettled = $this->calculateDaySettled($merchant_id, date('Y-m-d'));

        // 预付余额 = 当前预付 - 当天总汇后
        return number_format($currentPrepaid - $todaySettled, 2, '.', '');
    }

    /**
     * 获取商户当前预付（从预付记录表中获取最后一次变更后金额）
     * @param int $merchant_id 商户ID
     * @return float
     */
    private function getCurrentPrepaid($merchant_id)
    {
        $prepaidRecord = Db::name('prepaid')
            ->where('merchant_id', $merchant_id)
            ->order('createtime', 'desc')
            ->find();

        return $prepaidRecord ? floatval($prepaidRecord['after_amount']) : 0;
    }

    /**
     * 计算指定日期的总汇后金额
     * @param int $merchant_id 商户ID
     * @param string $date 日期格式 Y-m-d
     * @return float 总汇后金额
     */
    private function calculateDaySettled($merchant_id, $date)
    {
        // 获取指定日期的开始和结束时间
        $dayStart = strtotime($date . ' 00:00:00');
        $dayEnd = strtotime($date . ' 23:59:59');

        $totalSettled = 0;
        $paytypes = ['huya', 'yahu'];

        foreach ($paytypes as $paytype) {
            // 查询当天所有该paytype的费率设置点
            $rateList = Db::name('rate')
                ->where('merchant_id', $merchant_id)
                ->where('paytype', $paytype)
                ->where('createtime', 'between', [$dayStart, $dayEnd])
                ->order('createtime', 'asc')
                ->select();

            // 分段点：先收集所有分段点（含0点、所有设置点、23:59:59）
            $splitPoints = [$dayStart];
            foreach ($rateList as $rateRow) {
                $splitPoints[] = $rateRow['createtime'];
            }
            $splitPoints[] = $dayEnd;

            // 去重并排序
            $splitPoints = array_unique($splitPoints);
            sort($splitPoints);

            // 遍历分段计算汇后
            for ($i = 0; $i < count($splitPoints) - 1; $i++) {
                $segStart = $splitPoints[$i];
                $segEnd = $splitPoints[$i + 1];

                // 查找分段起始时间点前最近一次设置的费率
                $rate = Db::name('rate')
                    ->where('merchant_id', $merchant_id)
                    ->where('paytype', $paytype)
                    ->where('createtime', '<=', $segStart)
                    ->order('createtime', 'desc')
                    ->value('rate');
                $rate = $rate !== null ? $rate : 0;

                // 计算该分段的跑量
                $segmentAmount = $this->getOrderStats([
                    'merchant_id' => $merchant_id,
                    'callback_status' => 1,
                    'callback_time' => ['between', [$segStart, $segEnd]]
                ], $paytype, 'sum', 'amount');

                // 计算该分段的汇后
                $segmentSettled = $segmentAmount * (1 - $rate / 100);
                $totalSettled += $segmentSettled;
            }
        }

        return $totalSettled;
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param array $where 查询条件
     * @param string $type 统计类型 count/sum
     * @param string $field 字段名，sum时用
     * @return float|int
     */
    private function getOrderStats($where, $paytype, $type = 'count', $field = 'id')
    {
        if ($type === 'count') {
            $main = \think\Db::name('order')->where($where)->where('paytype', $paytype)->count($field);
            $archive = \think\Db::name('order_history')->where($where)->where('paytype', $paytype)->count($field);
        } elseif ($type === 'sum') {
            $main = \think\Db::name('order')->where($where)->where('paytype', $paytype)->sum($field);
            $archive = \think\Db::name('order_history')->where($where)->where('paytype', $paytype)->sum($field);
        } else {
            $main = 0;
            $archive = 0;
        }
        return $main + $archive;
    }



    /**
     * 去除where条件中的表别名前缀（如m.）
     * @param array $where
     * @param string $prefix
     * @return array
     */
    private function stripAliasPrefix($where, $prefix = 'm.')
    {
        $result = [];
        foreach ($where as $k => $v) {
            if (strpos($k, $prefix) === 0) {
                $result[substr($k, strlen($prefix))] = $v;
            } else {
                $result[$k] = $v;
            }
        }
        return $result;
    }

    /**
     * 过滤掉不属于order表的字段
     * @param array $where
     * @return array
     */
    private function filterOrderFields($where)
    {
        $orderFields = [
            'id',
            'user_id',
            'merchant_id',
            'account_id',
            'out_trade_no',
            'dy_order_id',
            'pay_status',
            'paytype',
            'amount',
            'callback_status',
            'create_time',
            'callback_time',
            'remark'
        ];
        $result = [];
        foreach ($where as $k => $v) {
            if (in_array($k, $orderFields)) {
                $result[$k] = $v;
            }
        }
        return $result;
    }
}
