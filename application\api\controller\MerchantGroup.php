<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;

/**
 * 商户分组接口
 */
class MerchantGroup extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = [];

    /**
     * 分组列表
     */
    public function list()
    {
        $params = [
            'page'   => input('page/d', 1),
            'limit'  => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status' => input('status/d', ''),
        ];

        $where = [];
        if ($params['name'] !== '') {
            $where['name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['status'] !== '') {
            $where['status'] = $params['status'];
        }

        $list = Db::name('merchant_group')
            ->field('id,name,status,remark,createtime,updatetime')
            ->where($where)
            ->order('id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        $total = Db::name('merchant_group')->where($where)->count();

        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit'],
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 新增分组
     */
    public function add()
    {
        $name = input('name/s', '');
        $status = input('status/d', 1);
        $remark = input('remark/s', '');

        // 支持数组或逗号分隔字符串
        $merchantIds = input('merchant_ids/a', null);
        if ($merchantIds === null) {
            $idsStr = input('merchant_ids/s', '');
            if ($idsStr !== '') {
                $merchantIds = array_values(array_filter(array_map('intval', explode(',', $idsStr))));
            }
        }

        if ($name === '') {
            $this->error('分组名称不能为空');
        }
        $exists = Db::name('merchant_group')->where('name', $name)->find();
        if ($exists) {
            $this->error('分组名称已存在');
        }

        Db::startTrans();
        try {
            $now = time();
            $data = [
                'name'       => $name,
                'status'     => $status,
                'remark'     => $remark,
                'createtime' => $now,
                'updatetime' => $now,
            ];

            $groupId = Db::name('merchant_group')->insertGetId($data);
            if (!$groupId) {
                throw new Exception('新增分组失败');
            }

            if (is_array($merchantIds) && count($merchantIds) > 0) {
                // 校验商户是否存在
                $validIds = Db::name('merchant')->where('id', 'in', $merchantIds)->column('id');
                if (count($validIds) !== count(array_unique($merchantIds))) {
                    throw new Exception('存在无效的商户ID');
                }
                // 直接将这些商户绑定到该分组
                Db::name('merchant')->where('id', 'in', $merchantIds)->update(['group_id' => $groupId]);
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('新增分组成功');
    }

    /**
     * 更新分组
     */
    public function update()
    {
        $id = input('id/d');
        if (!$id) {
            $this->error('分组ID不能为空');
        }
        $group = Db::name('merchant_group')->where('id', $id)->find();
        if (!$group) {
            $this->error('分组不存在');
        }

        $name = input('name/s', null);
        $status = input('status/d', null);
        $remark = input('remark/s', null);

        // 支持数组或逗号分隔字符串
        $merchantIds = input('merchant_ids/a', null);
        if ($merchantIds === null) {
            $idsStr = input('merchant_ids/s', '');
            if ($idsStr !== '') {
                $merchantIds = array_values(array_filter(array_map('intval', explode(',', $idsStr))));
            }
        }

        $update = [];
        if ($name !== null) {
            if ($name === '') {
                $this->error('分组名称不能为空');
            }
            $exists = Db::name('merchant_group')->where('name', $name)->where('id', '<>', $id)->find();
            if ($exists) {
                $this->error('分组名称已存在');
            }
            $update['name'] = $name;
        }
        if ($status !== null) {
            $update['status'] = $status;
        }
        if ($remark !== null) {
            $update['remark'] = $remark;
        }

        Db::startTrans();
        try {
            if (!empty($update)) {
                $update['updatetime'] = time();
                $res = Db::name('merchant_group')->where('id', $id)->update($update);
                if ($res === false) {
                    throw new Exception('更新分组失败');
                }
            }

            if (is_array($merchantIds)) {
                // 校验商户是否存在
                if (count($merchantIds) > 0) {
                    $validIds = Db::name('merchant')->where('id', 'in', $merchantIds)->column('id');
                    if (count($validIds) !== count(array_unique($merchantIds))) {
                        throw new Exception('存在无效的商户ID');
                    }
                }
                // 先将当前该分组下但不在新列表内的商户解绑
                $unbindWhere = ['group_id' => $id];
                if (count($merchantIds) > 0) {
                    Db::name('merchant')->where($unbindWhere)->where('id', 'not in', $merchantIds)->update(['group_id' => null]);
                } else {
                    // 如果传了空数组，表示清空该分组商户
                    Db::name('merchant')->where($unbindWhere)->update(['group_id' => null]);
                }
                // 将列表内商户绑定到该分组
                if (count($merchantIds) > 0) {
                    Db::name('merchant')->where('id', 'in', $merchantIds)->update(['group_id' => $id]);
                }
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('更新分组成功');
    }

    /**
     * 删除分组（将关联商户group_id置空）
     */
    public function delete()
    {
        $id = input('id/d');
        if (!$id) {
            $this->error('分组ID不能为空');
        }
        $group = Db::name('merchant_group')->where('id', $id)->find();
        if (!$group) {
            $this->error('分组不存在');
        }

        Db::startTrans();
        try {
            // 将该分组下的商户group_id置空
            Db::name('merchant')->where('group_id', $id)->update(['group_id' => null]);
            // 删除分组
            Db::name('merchant_group')->where('id', $id)->delete();
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('删除分组成功');
    }

    /**
     * 分组详情
     */
    public function detail()
    {
        $id = input('id/d');
        if (!$id) {
            $this->error('分组ID不能为空');
        }
        $group = Db::name('merchant_group')->where('id', $id)->find();
        if (!$group) {
            $this->error('分组不存在');
        }
        // 绑定商户ID数组
        $merchantIds = Db::name('merchant')->where('group_id', $id)->column('id');
        $group['merchant_ids'] = array_values($merchantIds);
        $this->success('success', $group);
    }
}
