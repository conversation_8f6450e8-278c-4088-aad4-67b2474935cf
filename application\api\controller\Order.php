<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 订单接口
 */
class Order extends Api
{

    protected $noNeedLogin = [];
    protected $noNeedRight = ['export'];

    /**
     * 获取订单列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'out_trade_no'   => input('out_trade_no/s', ''),
            'dy_order_id'   => input('dy_order_id/s', ''),
            'pay_status'     => input('pay_status/d', ''),
            'paytype'     => input('paytype/s', ''),
            'amount_min'   => input('amount_min/d', ''),
            'amount_max'   => input('amount_max/d', ''),
            'callback_status'     => input('callback_status/d', ''),
            'start_time'     => input('start_time/s', ''),
            'end_time'       => input('end_time/s', ''),
            'merchant_name'  => input('merchant_name/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的订单
            $where['o.user_id'] = $this->auth->id;
        }
        // ========== 核心优化1：默认时间限制 ==========
        $defaultHours = 3; // 默认查询最近3小时
        if (empty($params['start_time']) && empty($params['end_time'])) {
            $where['o.create_time'] = ['>=', time() - $defaultHours * 3600];
        }
        // 构建查询条件
        if ($params['out_trade_no'] !== '') {
            $where['o.out_trade_no'] = ['like', "%{$params['out_trade_no']}%"];
        }
        if ($params['dy_order_id'] !== '') {
            $where['o.dy_order_id'] = ['like', "%{$params['dy_order_id']}%"];
        }
        if ($params['pay_status'] !== '') {
            $where['o.pay_status'] = $params['pay_status'];
        }
        if ($params['paytype'] !== '') {
            $where['o.paytype'] = $params['paytype'];
        }
        if ($params['callback_status'] !== '') {
            $where['o.callback_status'] = $params['callback_status'];
        }
        if ($params['merchant_name'] !== '') {
            $where['m.name'] = ['like', "%{$params['merchant_name']}%"];
        }
        // 金额区间筛选
        if ($params['amount_min'] !== '' && $params['amount_max'] !== '') {
            $where['o.amount'] = ['between', [$params['amount_min'], $params['amount_max']]];
        } elseif ($params['amount_min'] !== '') {
            $where['o.amount'] = ['>=', $params['amount_min']];
        } elseif ($params['amount_max'] !== '') {
            $where['o.amount'] = ['<=', $params['amount_max']];
        }
        if ($params['start_time'] !== '') {
            $where['o.create_time'] = ['>=', strtotime($params['start_time'])];
        }
        if ($params['end_time'] !== '') {
            if (isset($where['o.create_time'])) {
                // 如果已经设置了开始时间，则添加结束时间条件
                $where['o.create_time'] = ['between', [strtotime($params['start_time']), strtotime($params['end_time'])]];
            } else {
                // 如果没有设置开始时间，只设置结束时间条件
                $where['o.create_time'] = ['<=', strtotime($params['end_time'])];
            }
        }

        // 分页参数
        $page = max(1, intval($params['page']));
        $limit = max(1, intval($params['limit']));
        $offset = ($page - 1) * $limit;

        // ========== 修复1：先查询总数，确保分页准确 ==========
        $total = $this->getOrderListCount($where);

        // ========== 修复2：简化分页逻辑，统一查询主表和归档表 ==========
        $list = $this->getOrderListData($where, $offset, $limit);

        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $page,
            'limit' => $limit
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 获取订单详情
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('订单ID不能为空');
        }

        // 查询订单详情
        $order = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('u.id as uid, o.id, o.out_trade_no, o.pay_trade_no, o.paytype, 
                    o.create_time, o.callback_time, o.amount, o.pay_status, 
                    o.callback_status, o.remark, m.name as merchant_name, 
                    a.name as account_name, u.nickname as user_nickname')
            ->where('o.id', $id)
            ->find();

        // 检查订单是否存在
        if (!$order) {
            $this->error('订单不存在');
        }

        // 权限检查：非管理员只能查看自己的订单
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $order['uid'] === $this->auth->id;

        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限查看该订单');
        }

        $this->success('查询成功', $order);
    }

    /**
     * 订单回调
     */
    public function callback()
    {
        $params = input();

        // 检查参数
        if (empty($params['id'])) {
            $this->error('订单ID不能为空');
        }

        // 使用 find 方法查询订单，确保获取正确的订单数据
        $order = \app\common\model\Order::where('id', $params['id'])->find();
        $orderTable = 'order';
        $isArchive = false;
        if (!$order) {
            // 主表查不到再查归档表
            $order = \think\Db::name('order_history')->where('id', $params['id'])->find();
            if (!$order) {
                $this->error('订单不存在');
            }
            $orderTable = 'order_history';
            $isArchive = true;
        }

        // 不能重复回调
        if ((isset($order->callback_status) && $order->callback_status == 1) || (isset($order['callback_status']) && $order['callback_status'] == 1)) {
            $this->error('订单已回调');
        }

        // 权限检查：非管理员只能回调自己的订单
        $isAdmin = $this->auth->group_id === 1;
        $userId = isset($order['user_id']) ? $order['user_id'] : (isset($order->user_id) ? $order->user_id : null);
        $isOwner = $userId === $this->auth->id;

        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限回调该订单');
        }
        $payTradeNo = isset($order['pay_trade_no']) ? $order['pay_trade_no'] : (isset($order->pay_trade_no) ? $order->pay_trade_no : null);
        if (empty($payTradeNo)) {
            $this->error('未出单不能回调');
        }
        $accountId = isset($order['account_id']) ? $order['account_id'] : (isset($order->account_id) ? $order->account_id : null);
        $account = Db::name('account')->where('id', $accountId)->find();
        $accountConfig = json_decode($account['config'], true);
        $cookie = $accountConfig['cookie'];
        $res = \huya\Service::status($payTradeNo, $cookie);
        if ($res->code !== 0) $this->error($res->message);
        if ($res->data->status !== "PAY_SUCCESS") $this->error("订单未支付");
        // 更新支付状态
        if ($isArchive) {
            Db::name('order_history')->where('id', $order['id'])->update(['pay_status' => 1]);
        } else {
            Db::name('order')->where('id', $order['id'])->update(['pay_status' => 1]);
        }
        $merchantId = isset($order['merchant_id']) ? $order['merchant_id'] : (isset($order->merchant_id) ? $order->merchant_id : null);
        $merchant = Db::name('merchant')->where('id', $merchantId)->find();
        $callbackUrl = isset($order['callback_url']) ? $order['callback_url'] : (isset($order->callback_url) ? $order->callback_url : null);
        $outTradeNo = isset($order['out_trade_no']) ? $order['out_trade_no'] : (isset($order->out_trade_no) ? $order->out_trade_no : null);
        $amount = isset($order['amount']) ? $order['amount'] : (isset($order->amount) ? $order->amount : null);
        $paytype = isset($order['paytype']) ? $order['paytype'] : (isset($order->paytype) ? $order->paytype : null);
        $res = \app\common\library\Order::merchantsCallback($callbackUrl, $merchant['key'], $outTradeNo, $amount, $paytype, 1, $userId);
        if ($res) {
            // 更新回调状态和时间
            if ($isArchive) {
                Db::name('order_history')->where('id', $order['id'])->update(['callback_status' => 1, 'callback_time' => time()]);
            } else {
                $order->callback_status = 1;
                $order->callback_time = time();
                $order->save();
            }
            $this->success('订单回调成功');
        }
        $this->error('订单回调失败');
    }

    /**
     * 订单导出
     */
    public function export()
    {
        // 获取查询参数（与list接口一致）
        $params = [
            'out_trade_no'   => input('out_trade_no/s', ''),
            'dy_order_id'   => input('dy_order_id/s', ''),
            'pay_status'     => input('pay_status/d', ''),
            'paytype'     => input('paytype/s', ''),
            'amount_min'   => input('amount_min/d', ''),
            'amount_max'   => input('amount_max/d', ''),
            'callback_status'     => input('callback_status/d', ''),
            'start_time'     => input('start_time/s', ''),
            'end_time'       => input('end_time/s', ''),
            'merchant_name'  => input('merchant_name/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能导出自己的订单
            $where['o.user_id'] = $this->auth->id;
        }
        $defaultHours = 3; // 默认查询最近3小时
        if (empty($params['start_time']) && empty($params['end_time'])) {
            $where['o.create_time'] = ['>=', time() - $defaultHours * 3600];
        }
        if ($params['out_trade_no'] !== '') {
            $where['o.out_trade_no'] = ['like', "%{$params['out_trade_no']}%"];
        }
        if ($params['dy_order_id'] !== '') {
            $where['o.dy_order_id'] = ['like', "%{$params['dy_order_id']}%"];
        }
        if ($params['pay_status'] !== '') {
            $where['o.pay_status'] = $params['pay_status'];
        }
        if ($params['paytype'] !== '') {
            $where['o.paytype'] = $params['paytype'];
        }
        if ($params['callback_status'] !== '') {
            $where['o.callback_status'] = $params['callback_status'];
        }
        if ($params['merchant_name'] !== '') {
            $where['m.name'] = ['like', "%{$params['merchant_name']}%"];
        }
        if ($params['amount_min'] !== '' && $params['amount_max'] !== '') {
            $where['o.amount'] = ['between', [$params['amount_min'], $params['amount_max']]];
        } elseif ($params['amount_min'] !== '') {
            $where['o.amount'] = ['>=', $params['amount_min']];
        } elseif ($params['amount_max'] !== '') {
            $where['o.amount'] = ['<=', $params['amount_max']];
        }
        if ($params['start_time'] !== '') {
            $where['o.create_time'] = ['>=', strtotime($params['start_time'])];
        }
        if ($params['end_time'] !== '') {
            if (isset($where['o.create_time'])) {
                $where['o.create_time'] = ['between', [strtotime($params['start_time']), strtotime($params['end_time'])]];
            } else {
                $where['o.create_time'] = ['<=', strtotime($params['end_time'])];
            }
        }

        // 查询所有数据（主表+归档表合并导出）
        $orderWhere = $this->stripAliasPrefix($where, 'o.');
        $orderWhere = $this->filterOrderFields($orderWhere);
        $list1 = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('o.id,o.out_trade_no,o.dy_order_id,o.paytype,o.create_time,o.callback_time,o.amount,o.pay_status,o.callback_status,o.remark,m.name as merchant_name,a.name as account_name,u.nickname as user_nickname')
            ->where($where)
            ->order('o.id', 'desc')
            ->select();
        $list2 = \think\Db::name('order_history')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('o.id,o.out_trade_no,o.dy_order_id,o.paytype,o.create_time,o.callback_time,o.amount,o.pay_status,o.callback_status,o.remark,m.name as merchant_name,a.name as account_name,u.nickname as user_nickname')
            ->where($where)
            ->order('o.id', 'desc')
            ->select();
        $list = array_merge($list1, $list2);
        // 按id倒序排序（保证主表和归档表合并后顺序一致）
        usort($list, function ($a, $b) {
            return $b['id'] <=> $a['id'];
        });
        if (empty($list)) {
            $this->error('没有找到符合条件的订单数据');
        }

        // 准备导出数据
        $exportData = [];
        // 添加表头
        $exportData[] = [
            '订单ID',
            '商户名称',
            '账号名称',
            '用户昵称',
            '商户订单号',
            'dy_order_id',
            '支付类型',
            '下单时间',
            '回调时间',
            '金额',
            '支付状态',
            '回调状态',
            '备注'
        ];
        // 添加数据行
        foreach ($list as $item) {
            $payStatusText = $item['pay_status'] == 1 ? '已支付' : '未支付';
            $callbackStatusText = $item['callback_status'] == 1 ? '已回调' : '未回调';
            $exportData[] = [
                $item['id'],
                $item['merchant_name'],
                $item['account_name'],
                $item['user_nickname'],
                $item['out_trade_no'],
                $item['dy_order_id'],
                $item['paytype'],
                $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '',
                $item['callback_time'] ? date('Y-m-d H:i:s', $item['callback_time']) : '',
                $item['amount'],
                $payStatusText,
                $callbackStatusText,
                $item['remark']
            ];
        }
        $filename = '订单列表_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'export' . DS;
        // 确保目录存在
        if (!is_dir($filepath)) {
            if (!mkdir($filepath, 0755, true)) {
                throw new \Exception('创建导出目录失败');
            }
        }
        $fullpath = $filepath . $filename;
        // 写入CSV文件
        $fp = fopen($fullpath, 'w');
        if ($fp === false) {
            throw new \Exception('无法创建导出文件');
        }
        // 添加BOM头，解决中文乱码问题
        fwrite($fp, "\xEF\xBB\xBF");
        foreach ($exportData as $row) {
            if (fputcsv($fp, $row) === false) {
                fclose($fp);
                throw new \Exception('写入CSV数据失败');
            }
        }
        fclose($fp);
        // 检查文件是否成功创建
        if (!file_exists($fullpath)) {
            throw new \Exception('导出文件创建失败');
        }
        // 清理7天前的导出文件
        $this->cleanOldExportFiles($filepath);
        // 返回下载链接
        $downloadUrl = $this->request->domain() . '/uploads/export/' . $filename;
        $this->success('导出成功', [
            'download_url' => $downloadUrl,
            'filename' => $filename,
            'total_count' => count($list)
        ]);
    }

    private function cleanOldExportFiles(
        $filepath
    ) {
        try {
            $files = glob($filepath . '*.csv');
            $expireTime = time() - 7 * 24 * 60 * 60; // 7天前

            foreach ($files as $file) {
                if (filemtime($file) < $expireTime) {
                    unlink($file);
                }
            }
        } catch (\Exception $e) {
            // 清理失败不影响主流程，只记录日志
            \think\Log::warning('清理过期导出文件失败：' . $e->getMessage());
        }
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param array $where 查询条件
     * @param string $type 统计类型 count/sum
     * @param string $field 字段名，sum时用
     * @return float|int
     */
    private function getOrderStats($where, $type = 'count', $field = 'id')
    {
        if ($type === 'count') {
            $main = \think\Db::name('order')->where($where)->count($field);
            $archive = \think\Db::name('order_history')->where($where)->count($field);
        } elseif ($type === 'sum') {
            $main = \think\Db::name('order')->where($where)->sum($field);
            $archive = \think\Db::name('order_history')->where($where)->sum($field);
        } else {
            $main = 0;
            $archive = 0;
        }
        return $main + $archive;
    }

    /**
     * 去除where条件中的表别名前缀（如o.）
     * @param array $where
     * @param string $prefix
     * @return array
     */
    private function stripAliasPrefix($where, $prefix = 'o.')
    {
        $result = [];
        foreach ($where as $k => $v) {
            if (strpos($k, $prefix) === 0) {
                $result[substr($k, strlen($prefix))] = $v;
            } else {
                $result[$k] = $v;
            }
        }
        return $result;
    }

    /**
     * 过滤掉不属于order表的字段
     * @param array $where
     * @return array
     */
    private function filterOrderFields($where)
    {
        $orderFields = [
            'id',
            'user_id',
            'merchant_id',
            'account_id',
            'out_trade_no',
            'dy_order_id',
            'pay_status',
            'paytype',
            'amount',
            'callback_status',
            'create_time',
            'callback_time',
            'remark'
        ];
        $result = [];
        foreach ($where as $k => $v) {
            if (in_array($k, $orderFields)) {
                $result[$k] = $v;
            }
        }
        return $result;
    }

    /**
     * 获取订单列表总数
     * @param array $where 查询条件
     * @return int
     */
    private function getOrderListCount($where)
    {
        // 主表查询
        $count1 = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->where($where)
            ->count('o.id');

        // 归档表查询
        $count2 = \think\Db::name('order_history')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->where($where)
            ->count('o.id');

        return $count1 + $count2;
    }

    /**
     * 获取订单列表数据
     * @param array $where 查询条件
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @return array
     */
    private function getOrderListData($where, $offset, $limit)
    {
        // 先查询主表数据
        $list1 = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('o.id,o.out_trade_no,o.dy_order_id,o.paytype,o.create_time,
                    o.callback_time,o.amount,o.pay_status,o.callback_status,o.remark,
                    m.name as merchant_name,a.name as account_name,u.nickname as user_nickname')
            ->where($where)
            ->order('o.id', 'desc')
            ->select();

        // 查询归档表数据
        $list2 = \think\Db::name('order_history')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('o.id,o.out_trade_no,o.dy_order_id,o.paytype,o.create_time,
                    o.callback_time,o.amount,o.pay_status,o.callback_status,o.remark,
                    m.name as merchant_name,a.name as account_name,u.nickname as user_nickname')
            ->where($where)
            ->order('o.id', 'desc')
            ->select();

        // 合并数据并按ID倒序排序
        $allList = array_merge($list1, $list2);
        usort($allList, function ($a, $b) {
            return $b['id'] <=> $a['id'];
        });

        // 分页处理
        return array_slice($allList, $offset, $limit);
    }
}
