<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Prepaid;
use app\common\model\Merchant;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 预付记录控制器
 */
class Prepaidrecord extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 获取预付记录列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page' => input('page/d', 1),
            'limit' => input('limit/d', 10),
            'merchant_id' => input('merchant_id/d', ''),
            'change_type' => input('change_type/s', ''), // 变更类型：increase/decrease
            'start_time' => input('start_time/s', ''),
            'end_time' => input('end_time/s', ''),
            'remark' => input('remark/s', '')
        ];

        $where = [];

        // 构建查询条件
        if ($params['merchant_id'] !== '') {
            $where['merchant_id'] = $params['merchant_id'];
        }

        if ($params['change_type'] !== '') {
            if ($params['change_type'] === 'increase') {
                $where['change_amount'] = ['>', 0];
            } elseif ($params['change_type'] === 'decrease') {
                $where['change_amount'] = ['<', 0];
            }
        }

        if ($params['remark'] !== '') {
            $where['remark'] = ['like', "%{$params['remark']}%"];
        }

        // 时间范围查询
        if ($params['start_time'] !== '') {
            $startTime = strtotime($params['start_time']);
            if ($startTime !== false) {
                $where['createtime'] = ['>=', $startTime];
            }
        }

        if ($params['end_time'] !== '') {
            $endTime = strtotime($params['end_time'] . ' 23:59:59');
            if ($endTime !== false) {
                if (isset($where['createtime'])) {
                    $where['createtime'] = ['between', [$where['createtime'][1], $endTime]];
                } else {
                    $where['createtime'] = ['<=', $endTime];
                }
            }
        }

        // 查询数据
        $list = Db::name('prepaid')
            ->alias('p')
            ->join('merchant m', 'p.merchant_id = m.id', 'LEFT')
            ->field('p.*, m.name as merchant_name')
            ->where($where)
            ->order('p.createtime', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 格式化数据
        foreach ($list as &$item) {
            $item['createtime_text'] = date('Y-m-d H:i:s', $item['createtime']);
            $item['change_type'] = $item['change_amount'] > 0 ? '增加' : '减少';
            $item['change_amount_formatted'] = number_format(abs($item['change_amount']), 2, '.', '');
        }

        // 查询总数
        $total = Db::name('prepaid')
            ->alias('p')
            ->where($where)
            ->count();

        $data = [
            'total' => $total,
            'list' => $list,
            'page' => $params['page'],
            'limit' => $params['limit']
        ];

        $this->success('查询预付记录成功', $data);
    }

    /**
     * 获取预付记录详情
     */
    public function detail()
    {
        $id = input('id/d');

        if (!$id) {
            $this->error('记录ID不能为空');
        }

        $record = Db::name('prepaid')
            ->alias('p')
            ->join('merchant m', 'p.merchant_id = m.id', 'LEFT')
            ->field('p.*, m.name as merchant_name')
            ->where('p.id', $id)
            ->find();

        if (!$record) {
            $this->error('预付记录不存在');
        }

        // 格式化数据
        $record['createtime_text'] = date('Y-m-d H:i:s', $record['createtime']);
        $record['updatetime_text'] = date('Y-m-d H:i:s', $record['updatetime']);
        $record['change_type'] = $record['change_amount'] > 0 ? '增加' : '减少';
        $record['change_amount_formatted'] = number_format(abs($record['change_amount']), 2, '.', '');

        $this->success('获取预付记录详情成功', $record);
    }

    /**
     * 修改预付记录备注
     */
    public function updateRemark()
    {
        $id = input('id/d');
        $remark = input('remark/s', '');

        if (!$id) {
            $this->error('记录ID不能为空');
        }

        $record = Db::name('prepaid')->where('id', $id)->find();
        if (!$record) {
            $this->error('预付记录不存在');
        }

        try {
            // 更新备注
            Db::name('prepaid')
                ->where('id', $id)
                ->update([
                    'remark' => $remark,
                    'updatetime' => time()
                ]);

            $this->success('修改备注成功');
        } catch (Exception $e) {
            $this->error('修改备注失败：' . $e->getMessage());
        }
    }
}
