<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use huya;
use Proxy;

class AccountPull extends Command
{
    // 最大重试次数
    const MAX_RETRIES = 3;
    // 请求超时时间（秒）
    const TIMEOUT = 10;
    // 并发请求数量
    const CONCURRENT_REQUESTS = 5;
    // 执行间隔时间（秒）
    const INTERVAL = 3600 * 6; // 1小时执行一次

    protected function configure()
    {
        // php think account:pull
        $this->setName('account:pull')
            ->setDescription('定期拉取账号状态');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            try {
                $output->writeln('开始执行账号拉单任务...');
                $output->writeln('执行时间：' . date('Y-m-d H:i:s'));

                // 获取所有可用账号
                $accountList = $this->getAccountList();
                if (empty($accountList)) {
                    $output->writeln('没有找到可用的账号');
                    sleep(self::INTERVAL);
                    continue;
                }

                $output->writeln('共找到 ' . count($accountList) . ' 个账号需要处理');

                // 分批处理账号
                $accountChunks = array_chunk($accountList, self::CONCURRENT_REQUESTS);
                $successCount = 0;
                $failCount = 0;

                foreach ($accountChunks as $chunk) {
                    foreach ($chunk as $account) {
                        try {
                            $output->writeln("正在处理账号ID: {$account['id']}");

                            // 获取代理信息
                            $proxy = Proxy::getProxyByShenLong(1, "");
                            if (!$proxy) {
                                $output->writeln("账号 {$account['id']} 没有可用的代理");
                                $failCount++;
                                continue;
                            }

                            // 获取账号配置
                            $accountConfig = json_decode($account['config'], true);
                            $cookie = $accountConfig['cookie'];

                            // 尝试拉单
                            $res = huya\Pay::createOrder(100, $cookie, 'alipay', $proxy);

                            if (!$res) {
                                $output->writeln("账号 {$account['id']} 拉单失败：代理请求失败");
                                $failCount++;
                                continue;
                            }

                            if (!$res['success']) {
                                $output->writeln("账号 {$account['id']} 拉单失败：" . $res['msg']);
                                $this->updateAccountError($account['id'], $res['msg']);
                                $failCount++;
                                continue;
                            }

                            // 拉单成功，更新账号状态
                            $this->updateAccountStatus($account['id']);
                            $output->writeln("账号 {$account['id']} 拉单成功");
                            $successCount++;
                        } catch (\Exception $e) {
                            $output->writeln("账号 {$account['id']} 处理异常：" . $e->getMessage());
                            $this->updateAccountError($account['id'], $e->getMessage());
                            $failCount++;
                        }
                    }
                }

                $output->writeln("拉单任务完成！成功：{$successCount}，失败：{$failCount}");
                Log::write("账号拉单任务完成，成功：{$successCount}，失败：{$failCount}", 'info');
            } catch (\Exception $e) {
                $errorMsg = "执行账号拉单任务时发生错误：" . $e->getMessage();
                $output->writeln($errorMsg);
                Log::write($errorMsg, 'error');
            }

            // 休眠指定时间
            $output->writeln('等待' . self::INTERVAL . '秒后执行下一次拉单...');
            sleep(self::INTERVAL);
        }
    }

    /**
     * 获取可用的支付账号列表
     * @return array
     */
    protected function getAccountList()
    {
        return db('account')
            ->where('errormsg', '<>', '')
            ->select();
    }

    /**
     * 更新账号状态
     * @param int $accountId 账号ID
     */
    protected function updateAccountStatus($accountId)
    {
        db('account')->where('id', $accountId)->update([
            'errormsg' => '',
            'pulltime' => time(),
            'status' => 1
        ]);
    }

    /**
     * 更新账号错误信息
     * @param int $accountId 账号ID
     * @param string $errorMsg 错误信息
     */
    protected function updateAccountError($accountId, $errorMsg)
    {
        db('account')->where('id', $accountId)->update([
            'errormsg' => $errorMsg,
            'pulltime' => time()
        ]);
    }
}
