<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;

use think\console\Output;
use think\Db;
use Exception;

class DailySettlement extends Command
{
    protected function configure()
    {
        // php think settlement:daily -d
        $this->setName('settlement:daily')
            ->setDescription('每日预付结算任务（自动结算昨日数据）')
            ->addOption('daemon', 'd', null, '启动定时模式，持续运行并在每天0点5分自动执行结算');
    }

    protected function execute(Input $input, Output $output)
    {
        $isDaemon = $input->getOption('daemon');

        if ($isDaemon) {
            // 启动定时模式
            $this->runDaemonMode($output);
        } else {
            // 直接执行一次结算
            $this->runOnce($output);
        }
    }

    /**
     * 运行一次结算
     */
    private function runOnce(Output $output)
    {
        // 直接结算昨天的数据
        $yesterday = date('Y-m-d', strtotime('-1 day'));

        $output->writeln("<info>开始执行 {$yesterday} 的预付结算...</info>");

        try {
            $this->executeSettlement($yesterday, $output);
            $output->writeln('<info>结算任务执行完成</info>');
            return 0;
        } catch (Exception $e) {
            $output->writeln("<error>结算任务执行失败：{$e->getMessage()}</error>");
            return 1;
        }
    }

    /**
     * 运行定时模式
     */
    private function runDaemonMode(Output $output)
    {
        $output->writeln('<info>启动定时结算模式，将在每天0点5分自动执行...</info>');
        $output->writeln('<info>按 Ctrl+C 退出</info>');
        $output->writeln('');

        $lastSettlementDate = null;

        while (true) {
            $currentTime = time();
            $hour = date('H', $currentTime);
            $minute = date('i', $currentTime);
            $today = date('Y-m-d', $currentTime);

            // 检查是否到了执行时间（0点5分）且今天还没有执行过
            if ($hour == '00' && $minute == '05' && $lastSettlementDate !== $today) {
                $yesterday = date('Y-m-d', strtotime('-1 day', $currentTime));

                $output->writeln('');
                $output->writeln('<info>==========================================</info>');
                $output->writeln("<info>触发定时结算：{$yesterday}</info>");
                $output->writeln('<info>==========================================</info>');

                try {
                    $this->executeSettlement($yesterday, $output);
                    $lastSettlementDate = $today; // 记录今天已执行过
                    $output->writeln('<info>定时结算完成，继续等待...</info>');
                } catch (Exception $e) {
                    $output->writeln("<error>定时结算失败：{$e->getMessage()}</error>");
                }
            }

            // 每分钟检查一次
            sleep(60);
        }
    }

    /**
     * 执行结算任务
     */
    private function executeSettlement($date, Output $output)
    {
        // 获取所有商户
        $merchants = Db::name('merchant')
            ->field('id, name, status')
            ->select();

        if (empty($merchants)) {
            $output->writeln('<warning>没有找到商户</warning>');
            return;
        }

        $settlementCount = 0;
        $errorCount = 0;
        $totalSettlementAmount = 0;

        foreach ($merchants as $merchant) {
            try {
                $merchantId = $merchant['id'];
                $merchantName = $merchant['name'];
                $merchantStatus = $merchant['status'];

                // 显示商户状态信息
                $statusText = $merchantStatus == 1 ? '活跃' : '非活跃';
                $output->writeln("<info>处理商户 {$merchantName}(ID:{$merchantId}) [{$statusText}]</info>");

                // 检查是否已经结算过
                $existingSettlement = Db::name('prepaid')
                    ->where('merchant_id', $merchantId)
                    ->where('remark', 'like', "系统自动结算{$date}汇后金额%")
                    ->find();

                if ($existingSettlement) {
                    $output->writeln("<comment>商户 {$merchantName}(ID:{$merchantId}) {$date} 已结算，跳过</comment>");
                    continue;
                }

                // 计算昨日总汇后金额
                $yesterdaySettled = $this->calculateDaySettled($merchantId, $date);

                if ($yesterdaySettled <= 0) {
                    $output->writeln("<comment>商户 {$merchantName}(ID:{$merchantId}) {$date} 无汇后金额，跳过</comment>");
                    continue;
                }

                // 获取当前预付金额
                $currentPrepaid = $this->getCurrentPrepaid($merchantId);

                // 开始事务
                Db::startTrans();
                try {
                    // 插入预付记录
                    $prepaidData = [
                        'merchant_id' => $merchantId,
                        'before_amount' => $currentPrepaid,
                        'after_amount' => $currentPrepaid - $yesterdaySettled,
                        'change_amount' => -$yesterdaySettled,
                        'remark' => "系统自动结算{$date}汇后金额" . number_format($yesterdaySettled, 2, '.', ''),
                        'createtime' => time(),
                        'updatetime' => time()
                    ];

                    Db::name('prepaid')->insert($prepaidData);

                    // 提交事务
                    Db::commit();

                    $output->writeln("<info>商户 {$merchantName}(ID:{$merchantId}) 结算成功，汇后金额：" . number_format($yesterdaySettled, 2, '.', '') . "</info>");

                    $settlementCount++;
                    $totalSettlementAmount += $yesterdaySettled;
                } catch (Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    throw $e;
                }
            } catch (Exception $e) {
                $output->writeln("<error>商户 {$merchantName}(ID:{$merchantId}) 结算失败：{$e->getMessage()}</error>");
                $errorCount++;
            }
        }

        // 输出结算汇总
        $output->writeln('');
        $output->writeln('<info>=== 结算完成 ===</info>');
        $output->writeln("<info>结算日期：{$date}</info>");
        $output->writeln("<info>总商户数：" . count($merchants) . "</info>");
        $output->writeln("<info>成功结算商户数：{$settlementCount}</info>");
        $output->writeln("<info>失败商户数：{$errorCount}</info>");
        $output->writeln("<info>总结算金额：" . number_format($totalSettlementAmount, 2, '.', '') . "</info>");
    }

    /**
     * 获取商户当前预付（从预付记录表中获取最后一次变更后金额）
     */
    private function getCurrentPrepaid($merchant_id)
    {
        $prepaidRecord = Db::name('prepaid')
            ->where('merchant_id', $merchant_id)
            ->order('createtime', 'desc')
            ->find();

        return $prepaidRecord ? floatval($prepaidRecord['after_amount']) : 0;
    }

    /**
     * 计算指定日期的总汇后金额
     * @param int $merchant_id 商户ID
     * @param string $date 日期格式 Y-m-d
     * @return float 总汇后金额
     */
    private function calculateDaySettled($merchant_id, $date)
    {
        // 获取指定日期的开始和结束时间
        $dayStart = strtotime($date . ' 00:00:00');
        $dayEnd = strtotime($date . ' 23:59:59');

        $totalSettled = 0;
        $paytypes = ['huya', 'yahu'];

        foreach ($paytypes as $paytype) {
            // 查询当天所有该paytype的费率设置点
            $rateList = Db::name('rate')
                ->where('merchant_id', $merchant_id)
                ->where('paytype', $paytype)
                ->where('createtime', 'between', [$dayStart, $dayEnd])
                ->order('createtime', 'asc')
                ->select();

            // 分段点：先收集所有分段点（含0点、所有设置点、23:59:59）
            $splitPoints = [$dayStart];
            foreach ($rateList as $rateRow) {
                $splitPoints[] = $rateRow['createtime'];
            }
            $splitPoints[] = $dayEnd;

            // 去重并排序
            $splitPoints = array_unique($splitPoints);
            sort($splitPoints);

            // 遍历分段计算汇后
            for ($i = 0; $i < count($splitPoints) - 1; $i++) {
                $segStart = $splitPoints[$i];
                $segEnd = $splitPoints[$i + 1];

                // 查找分段起始时间点前最近一次设置的费率
                $rate = Db::name('rate')
                    ->where('merchant_id', $merchant_id)
                    ->where('paytype', $paytype)
                    ->where('createtime', '<=', $segStart)
                    ->order('createtime', 'desc')
                    ->value('rate');
                $rate = $rate !== null ? $rate : 0;

                // 计算该分段的跑量
                $segmentAmount = $this->getOrderStats([
                    'merchant_id' => $merchant_id,
                    'callback_status' => 1,
                    'callback_time' => ['between', [$segStart, $segEnd]]
                ], $paytype, 'sum', 'amount');

                // 计算该分段的汇后
                $segmentSettled = $segmentAmount * (1 - $rate / 100);
                $totalSettled += $segmentSettled;
            }
        }

        return $totalSettled;
    }

    /**
     * 合并主表(order)和归档表(order_history)的统计数据
     * @param array $where 查询条件
     * @param string $paytype 支付类型
     * @param string $type 统计类型 count/sum
     * @param string $field 字段名，sum时用
     * @return int|float
     */
    private function getOrderStats($where, $paytype, $type = 'count', $field = 'id')
    {
        $where['paytype'] = $paytype;

        // 主表统计
        $mainResult = Db::name('order')
            ->where($where)
            ->$type($field);

        // 归档表统计
        $historyResult = Db::name('order_history')
            ->where($where)
            ->$type($field);

        if ($type == 'count') {
            return intval($mainResult) + intval($historyResult);
        } else {
            return floatval($mainResult) + floatval($historyResult);
        }
    }
}
