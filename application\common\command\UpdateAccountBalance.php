<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use huya\Service;

class UpdateAccountBalance extends Command
{
    // 最大重试次数
    const MAX_RETRIES = 3;
    // 请求超时时间（秒）
    const TIMEOUT = 10;
    // 并发请求数量
    const CONCURRENT_REQUESTS = 10;

    protected function configure()
    {
        // php think update:account-balance
        $this->setName('update:account-balance')
            ->setDescription('异步更新所有账号余额');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            try {
                $output->writeln('开始异步更新账号余额...');
                $output->writeln('执行时间：' . date('Y-m-d H:i:s'));

                // 获取所有有效账号
                $accounts = Db::name('account')
                    ->where('status', 1)
                    ->where('config', '<>', '')
                    ->select();

                if (empty($accounts)) {
                    $output->writeln('没有找到需要更新的账号');
                    sleep(60);
                    continue;
                }

                $output->writeln('共找到 ' . count($accounts) . ' 个账号需要更新');

                // 分批处理账号
                $accountChunks = array_chunk($accounts, self::CONCURRENT_REQUESTS);
                $successCount = 0;
                $failCount = 0;

                foreach ($accountChunks as $chunk) {
                    $this->processAccountChunk($chunk, $output, $successCount, $failCount);
                }

                $output->writeln("异步更新完成！成功：{$successCount}，失败：{$failCount}");
                Log::write("账号余额异步更新完成，成功：{$successCount}，失败：{$failCount}", 'info');
            } catch (\Exception $e) {
                $errorMsg = "异步更新账号余额时发生错误：" . $e->getMessage();
                $output->writeln($errorMsg);
                Log::write($errorMsg, 'error');
            }

            // 休眠10秒
            $output->writeln('等待10秒后执行下一次更新...');
            sleep(10);
        }
    }





    /**
     * 处理一批账号（CURL多线程异步方式）
     */
    protected function processAccountChunk($accounts, $output, &$successCount, &$failCount)
    {
        $mh = curl_multi_init();
        $handles = [];
        $accountMap = [];

        // 初始化CURL句柄
        foreach ($accounts as $account) {
            $config = json_decode($account['config'], true);
            if (empty($config['cookie'])) {
                $output->writeln("账号ID {$account['id']} 配置错误：缺少cookie");
                $failCount++;
                continue;
            }

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => "http://8.138.170.147:3000/proxy.php?target_url=https://charge.huya.com/account/balance",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => self::TIMEOUT,
                CURLOPT_CONNECTTIMEOUT => self::TIMEOUT,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => '',
                CURLOPT_HTTPHEADER => [
                    'Cookie: ' . $config['cookie'],
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'Accept: application/json, text/plain, */*',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                    // 'Accept-Encoding: gzip, deflate, br',
                    'Connection: keep-alive',
                    'Referer: https://charge.huya.com/'
                ]
            ]);

            $handles[] = $ch;
            $accountMap[] = $account['id'];
            curl_multi_add_handle($mh, $ch);
        }

        // 执行并发请求
        $running = null;
        do {
            $status = curl_multi_exec($mh, $running);
            if ($status > CURLM_OK) {
                break;
            }
            curl_multi_select($mh, self::TIMEOUT);
        } while ($running > 0);

        // 处理结果
        foreach ($handles as $index => $ch) {
            $accountId = $accountMap[$index];
            $result = curl_multi_getcontent($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);

            if ($httpCode == 200 && empty($curlError)) {
                try {
                    $data = json_decode($result, true);
                    if (isset($data['data']['useableBalance'])) {
                        $balance = $data['data']['useableBalance'];
                        Db::name('account')->where('id', $accountId)->update(['diamond' => $balance]);
                        $output->writeln("账号ID {$accountId} 余额更新成功: {$balance}");
                        $successCount++;
                    } else {
                        // $output->writeln($result);
                        $output->writeln("账号ID {$accountId} 余额更新失败：数据格式错误");
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    $output->writeln("账号ID {$accountId} 余额更新失败：" . $e->getMessage());
                    $failCount++;
                }
            } else {
                $output->writeln("账号ID {$accountId} 请求失败：HTTP {$httpCode}, CURL错误: {$curlError}");
                $failCount++;
            }

            curl_multi_remove_handle($mh, $ch);
            curl_close($ch);
        }

        curl_multi_close($mh);
    }
}
