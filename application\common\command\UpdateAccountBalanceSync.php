<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Log;
use huya\Service;

class UpdateAccountBalanceSync extends Command
{
    protected function configure()
    {
        // php think update:account-balance-sync
        $this->setName('update:account-balance-sync')
            ->setDescription('同步更新所有账号余额');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            try {
                $output->writeln('开始更新账号余额...');
                $output->writeln('执行时间：' . date('Y-m-d H:i:s'));

                // 获取所有有效账号
                $accounts = Db::name('account')
                    ->where('status', 1)
                    ->where('config', '<>', '')
                    ->select();

                if (empty($accounts)) {
                    $output->writeln('没有找到需要更新的账号');
                    sleep(60);
                    continue;
                }

                $output->writeln('共找到 ' . count($accounts) . ' 个账号需要更新');

                $successCount = 0;
                $failCount = 0;

                // 逐个处理账号
                foreach ($accounts as $account) {
                    try {
                        $config = json_decode($account['config'], true);
                        if (empty($config['cookie'])) {
                            $output->writeln("账号ID {$account['id']} 配置错误：缺少cookie");
                            $failCount++;
                            continue;
                        }

                        // 调用同步方法获取余额
                        $balance = Service::getUseableBalance($config['cookie']);

                        if ($balance === '未登录') {
                            $output->writeln("账号ID {$account['id']} 余额更新失败：未登录");
                            $failCount++;
                            continue;
                        }

                        // 更新数据库
                        Db::name('account')->where('id', $account['id'])->update([
                            'diamond' => $balance
                        ]);

                        $output->writeln("账号ID {$account['id']} 余额更新成功: {$balance}");
                        $successCount++;
                    } catch (\Exception $e) {
                        $output->writeln("账号ID {$account['id']} 余额更新失败：" . $e->getMessage());
                        $failCount++;
                    }
                }

                $output->writeln("更新完成！成功：{$successCount}，失败：{$failCount}");
                Log::write("账号余额更新完成，成功：{$successCount}，失败：{$failCount}", 'info');
            } catch (\Exception $e) {
                $errorMsg = "更新账号余额时发生错误：" . $e->getMessage();
                $output->writeln($errorMsg);
                Log::write($errorMsg, 'error');
            }

            // 休眠60秒
            $output->writeln('等待60秒后执行下一次更新...');
            sleep(60);
        }
    }
}
