<?php

namespace app\common\model;

use think\Model;

class MerchantGroup extends Model
{
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [];

    // 关联：一个分组有多个商户
    public function merchants()
    {
        return $this->hasMany('Merchant', 'group_id', 'id');
    }
}
