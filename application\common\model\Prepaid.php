<?php

namespace app\common\model;

use think\Model;

/**
 * 预付记录模型
 */
class Prepaid extends Model
{
    // 表名
    protected $name = 'prepaid';

    // 自动写入时间戳
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 关联商户
    public function merchant()
    {
        return $this->belongsTo('Merchant', 'merchant_id', 'id');
    }

    /**
     * 获取商户当前预付金额
     * @param int $merchantId
     * @return float
     */
    public static function getCurrentPrepaid($merchantId)
    {
        $lastRecord = self::where('merchant_id', $merchantId)
            ->order('id', 'desc')
            ->find();

        return $lastRecord ? $lastRecord['after_amount'] : 0.00;
    }

    /**
     * 添加预付记录
     * @param int $merchantId
     * @param float $changeAmount
     * @param string $remark
     * @return bool
     */
    public static function addRecord($merchantId, $changeAmount, $remark = '')
    {
        $currentPrepaid = self::getCurrentPrepaid($merchantId);
        $newPrepaid = $currentPrepaid + $changeAmount;

        $data = [
            'merchant_id' => $merchantId,
            'before_amount' => $currentPrepaid,
            'after_amount' => $newPrepaid,
            'change_amount' => $changeAmount,
            'remark' => $remark,
            'createtime' => time(),
            'updatetime' => time()
        ];

        return self::create($data);
    }

    /**
     * 获取商户预付记录列表
     * @param int $merchantId
     * @param int $limit
     * @return array
     */
    public static function getRecords($merchantId, $limit = 10)
    {
        return self::where('merchant_id', $merchantId)
            ->order('id', 'desc')
            ->limit($limit)
            ->select();
    }
}
