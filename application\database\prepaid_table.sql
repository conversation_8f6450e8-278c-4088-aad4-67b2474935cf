-- ----------------------------
-- Table structure for fa_prepaid
-- ----------------------------
DROP TABLE IF EXISTS `fa_prepaid`;
CREATE TABLE `fa_prepaid` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户ID',
  `before_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前金额',
  `after_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后金额',
  `change_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更金额',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='预付记录表';

-- ----------------------------
-- Records of fa_prepaid
-- ---------------------------- 