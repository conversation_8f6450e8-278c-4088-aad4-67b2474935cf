-- ----------------------------
-- Table structure for fa_rate
-- ----------------------------
DROP TABLE IF EXISTS `fa_rate`;
CREATE TABLE `fa_rate` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `merchant_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商户ID',
  `paytype` varchar(32) NOT NULL DEFAULT '' COMMENT '支付类型(huya/yahu)',
  `rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '费率(百分比)',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_paytype` (`paytype`),
  KEY `idx_createtime` (`createtime`),
  <PERSON><PERSON>Y `idx_merchant_paytype_time` (`merchant_id`, `paytype`, `createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='费率记录表';

-- ----------------------------
-- Records of fa_rate
-- ----------------------------
