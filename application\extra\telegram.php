<?php

/**
 * Telegram机器人配置文件
 * 
 * 配置说明：
 * - bot_token: Telegram机器人Token
 * - chat_id: 目标聊天群组ID
 * - pay_types: 支付类型映射配置
 * - message_template: 消息模板配置
 * - api_settings: API相关配置
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */

return [
    // 机器人基础配置
    'bot_token' => '7517940946:AAHSQOj-cPFZ87o2EPp1hrRqYX8pRlqr810',
    'chat_id' => -4909053025,

    // 支付类型映射
    'pay_types' => [
        'wechat' => 'huya',    // 微信支付
        'alipay' => 'yahu'     // 支付宝支付
    ],

    // 消息模板配置
    'message_template' => [
        'title' => '⏰来自定时推送',
        'separator' => '————————————',
        'channel_type' => '通道类型：总计金额',
        'time_format' => 'Y-m-d H:i:s'
    ],

    // API配置
    'api_settings' => [
        'base_url' => 'https://api.telegram.org/bot',
        'timeout' => 30,           // 请求超时时间（秒）
        'retry_times' => 3,        // 重试次数
        'retry_delay' => 1         // 重试延迟（秒）
    ],

    // 命令配置
    'commands' => [
        'query_stats' => '/cx_ts',     // 查询统计数据
        'get_id' => '/id',             // 获取聊天ID
        'help' => '/help'              // 帮助信息
    ],

    // 日志配置
    'log' => [
        'enabled' => true,             // 是否启用日志
        'level' => 'info',             // 日志级别
        'file' => 'telegram.log'       // 日志文件名
    ],

    // 安全配置
    'security' => [
        'allowed_chat_ids' => [        // 允许的聊天ID列表
            -4909053025
        ],
        'rate_limit' => [              // 频率限制
            'enabled' => true,
            'max_requests' => 100,     // 最大请求数
            'time_window' => 3600      // 时间窗口（秒）
        ]
    ],

    // 统计配置
    'statistics' => [
        'time_ranges' => [
            'hour' => 3600,            // 一小时（秒）
            'day' => 86400             // 一天（秒）
        ],
        'cache_duration' => 300        // 缓存时间（秒）
    ]
];
