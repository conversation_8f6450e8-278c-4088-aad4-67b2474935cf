<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Prepaid;
use app\common\model\Merchant;
use fast\Http;
use Proxy;

class Telegram extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    // Telegram Bot配置
    protected $botToken = '**********************************************';
    protected $sysChatId = -1002469029724;

    public function index()
    {
        return $this->view->fetch();
    }

    public function messageTask()
    {
        // 获取 微信 最近一小时金额，今日金额，昨日金额（主表）
        $hourAmountWx = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWx = db('order')->whereTime('callback_time', 'today')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWx = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额（主表）
        $hourAmountZfb = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfb = db('order')->whereTime('callback_time', 'today')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfb = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 获取 微信 最近一小时金额，今日金额，昨日金额（归档表）
        $hourAmountWxArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWxArchive = db('order_history')->whereTime('callback_time', 'today')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWxArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额（归档表）
        $hourAmountZfbArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'today')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 计算总计金额（主表 + 归档表）
        $hourAmountWxTotal = $hourAmountWx + $hourAmountWxArchive;
        $todayAmountWxTotal = $todayAmountWx + $todayAmountWxArchive;
        $yesterdayAmountWxTotal = $yesterdayAmountWx + $yesterdayAmountWxArchive;

        $hourAmountZfbTotal = $hourAmountZfb + $hourAmountZfbArchive;
        $todayAmountZfbTotal = $todayAmountZfb + $todayAmountZfbArchive;
        $yesterdayAmountZfbTotal = $yesterdayAmountZfb + $yesterdayAmountZfbArchive;

        $hourAmountTotal = $hourAmountWxTotal + $hourAmountZfbTotal;
        $todayAmountTotal = $todayAmountWxTotal + $todayAmountZfbTotal;
        $yesterdayAmountTotal = $yesterdayAmountWxTotal + $yesterdayAmountZfbTotal;

        // 格式化显示金额
        $hourAmountWxTotal = number_format($hourAmountWxTotal, 2);
        $todayAmountWxTotal = number_format($todayAmountWxTotal, 2);
        $yesterdayAmountWxTotal = number_format($yesterdayAmountWxTotal, 2);

        $hourAmountZfbTotal = number_format($hourAmountZfbTotal, 2);
        $todayAmountZfbTotal = number_format($todayAmountZfbTotal, 2);
        $yesterdayAmountZfbTotal = number_format($yesterdayAmountZfbTotal, 2);

        $hourAmountTotal = number_format($hourAmountTotal, 2);
        $todayAmountTotal = number_format($todayAmountTotal, 2);
        $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);

        $message = urlencode("
<b>⏰来自定时推送</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>通道类型：微信</b>
<b>小时金额：" . $hourAmountWxTotal . "</b>
<b>今日金额：" . $todayAmountWxTotal . "</b>
<b>昨日金额：" . $yesterdayAmountWxTotal . "</b>
<b>————————————</b>
<b>通道类型：支付宝</b>
<b>小时金额：" . $hourAmountZfbTotal . "</b>
<b>今日金额：" . $todayAmountZfbTotal . "</b>
<b>昨日金额：" . $yesterdayAmountZfbTotal . "</b>
<b>————————————</b>
<b>通道类型：总计</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
        $url = "https://api.telegram.org/bot" . $this->botToken . "/sendMessage?chat_id=" . $this->sysChatId . "&text=" . $message . "&parse_mode=HTML";
        Http::get($url);
        echo 'ok';
    }

    public function webHook()
    {
        $data = file_get_contents('php://input');
        $data = json_decode($data, true);
        if (isset($data['message']) && isset($data['update_id'])) {
            $message = $data['message'];
            $chatId = $message['chat']['id'];
            $text = $message['text'] ?? '';

            // 判断是否为系统群
            if ($chatId == $this->sysChatId) {
                $this->handleSystemCommands($chatId, $text);
            } else {
                $this->handleMerchantCommands($chatId, $text);
            }
        }
    }

    /**
     * 处理系统群命令
     */
    protected function handleSystemCommands($chatId, $text)
    {
        switch ($text) {
            case '/查账':
                $this->sendSystemStatistics($chatId);
                break;
            case '/id':
                $this->sendMessage($chatId, "系统群ID: " . $chatId);
                break;
            default:
                break;
        }
    }

    /**
     * 处理商户群命令
     */
    protected function handleMerchantCommands($chatId, $text)
    {
        // 获取群组绑定的商户信息
        $merchant = Merchant::where('telegram_group_id', $chatId)->find();
        if (!$merchant) {
            $this->sendMessage($chatId, "❌ 该群组未绑定商户，请联系管理员");
            return;
        }

        $merchantId = $merchant['id'];
        $merchantName = $merchant['name'];

        switch ($text) {
            case '/查账':
                $this->sendMerchantStatistics($chatId, $merchantId, $merchantName);
                break;
            case '/id':
                $this->sendMessage($chatId, "群组ID: {$chatId}\n商户ID: {$merchantId}\n商户名称: {$merchantName}");
                break;
            case '/帮助':
                $this->sendHelpMessage($chatId);
                break;
            default:
                if (strpos($text, '/加预付') === 0) {
                    $this->addPrepaid($chatId, $merchantId, $merchantName, $text);
                } elseif (strpos($text, '/减预付') === 0) {
                    $this->reducePrepaid($chatId, $merchantId, $merchantName, $text);
                }
                break;
        }
    }

    /**
     * 发送系统统计数据
     */
    protected function sendSystemStatistics($chatId)
    {
        // 获取 微信 最近一小时金额，今日金额，昨日金额（主表）
        $hourAmountWx = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWx = db('order')->whereTime('callback_time', 'today')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWx = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额（主表）
        $hourAmountZfb = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfb = db('order')->whereTime('callback_time', 'today')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfb = db('order')->whereTime('callback_time', 'yesterday')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 获取 微信 最近一小时金额，今日金额，昨日金额（归档表）
        $hourAmountWxArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWxArchive = db('order_history')->whereTime('callback_time', 'today')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWxArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        // 获取 支付宝 最近一小时金额，今日金额，昨日金额（归档表）
        $hourAmountZfbArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'today')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 计算总计金额（主表 + 归档表）
        $hourAmountWxTotal = $hourAmountWx + $hourAmountWxArchive;
        $todayAmountWxTotal = $todayAmountWx + $todayAmountWxArchive;
        $yesterdayAmountWxTotal = $yesterdayAmountWx + $yesterdayAmountWxArchive;

        $hourAmountZfbTotal = $hourAmountZfb + $hourAmountZfbArchive;
        $todayAmountZfbTotal = $todayAmountZfb + $todayAmountZfbArchive;
        $yesterdayAmountZfbTotal = $yesterdayAmountZfb + $yesterdayAmountZfbArchive;

        $hourAmountTotal = $hourAmountWxTotal + $hourAmountZfbTotal;
        $todayAmountTotal = $todayAmountWxTotal + $todayAmountZfbTotal;
        $yesterdayAmountTotal = $yesterdayAmountWxTotal + $yesterdayAmountZfbTotal;

        // 格式化显示金额
        $hourAmountWxTotal = number_format($hourAmountWxTotal, 2);
        $todayAmountWxTotal = number_format($todayAmountWxTotal, 2);
        $yesterdayAmountWxTotal = number_format($yesterdayAmountWxTotal, 2);

        $hourAmountZfbTotal = number_format($hourAmountZfbTotal, 2);
        $todayAmountZfbTotal = number_format($todayAmountZfbTotal, 2);
        $yesterdayAmountZfbTotal = number_format($yesterdayAmountZfbTotal, 2);

        $hourAmountTotal = number_format($hourAmountTotal, 2);
        $todayAmountTotal = number_format($todayAmountTotal, 2);
        $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);

        $message = urlencode("
<b>⏰系统统计数据</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>通道类型：微信</b>
<b>小时金额：" . $hourAmountWxTotal . "</b>
<b>今日金额：" . $todayAmountWxTotal . "</b>
<b>昨日金额：" . $yesterdayAmountWxTotal . "</b>
<b>————————————</b>
<b>通道类型：支付宝</b>
<b>小时金额：" . $hourAmountZfbTotal . "</b>
<b>今日金额：" . $todayAmountZfbTotal . "</b>
<b>昨日金额：" . $yesterdayAmountZfbTotal . "</b>
<b>————————————</b>
<b>通道类型：总计</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
        $this->sendMessage($chatId, $message);
    }

    /**
     * 发送商户统计数据
     */
    protected function sendMerchantStatistics($chatId, $merchantId, $merchantName)
    {
        // 获取商户的微信统计数据
        $hourAmountWx = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWx = db('order')->whereTime('callback_time', 'today')->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWx = db('order')->whereTime('callback_time', 'yesterday')->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        // 获取商户的支付宝统计数据
        $hourAmountZfb = db('order')->whereTime('callback_time', '>=', time() - 3600)->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfb = db('order')->whereTime('callback_time', 'today')->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfb = db('order')->whereTime('callback_time', 'yesterday')->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 获取归档表数据
        $hourAmountWxArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $todayAmountWxArchive = db('order_history')->whereTime('callback_time', 'today')->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountWxArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('merchant_id', $merchantId)->where('paytype', 'huya')->where('pay_status', 1)->sum('amount');

        $hourAmountZfbArchive = db('order_history')->whereTime('callback_time', '>=', time() - 3600)->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $todayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'today')->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');
        $yesterdayAmountZfbArchive = db('order_history')->whereTime('callback_time', 'yesterday')->where('merchant_id', $merchantId)->where('paytype', 'yahu')->where('pay_status', 1)->sum('amount');

        // 计算总计
        $hourAmountWxTotal = $hourAmountWx + $hourAmountWxArchive;
        $todayAmountWxTotal = $todayAmountWx + $todayAmountWxArchive;
        $yesterdayAmountWxTotal = $yesterdayAmountWx + $yesterdayAmountWxArchive;

        $hourAmountZfbTotal = $hourAmountZfb + $hourAmountZfbArchive;
        $todayAmountZfbTotal = $todayAmountZfb + $todayAmountZfbArchive;
        $yesterdayAmountZfbTotal = $yesterdayAmountZfb + $yesterdayAmountZfbArchive;

        $hourAmountTotal = $hourAmountWxTotal + $hourAmountZfbTotal;
        $todayAmountTotal = $todayAmountWxTotal + $todayAmountZfbTotal;
        $yesterdayAmountTotal = $yesterdayAmountWxTotal + $yesterdayAmountZfbTotal;

        // 获取当前预付金额
        $currentPrepaid = Prepaid::getCurrentPrepaid($merchantId);

        // 计算当天总汇后金额
        $todaySettled = $this->calculateDaySettled($merchantId, date('Y-m-d'));

        // 计算预付余额 = 当前预付 - 当天总汇后
        $prepaidBalance = $currentPrepaid - $todaySettled;

        // 格式化显示金额
        $hourAmountWxTotal = number_format($hourAmountWxTotal, 2);
        $todayAmountWxTotal = number_format($todayAmountWxTotal, 2);
        $yesterdayAmountWxTotal = number_format($yesterdayAmountWxTotal, 2);

        $hourAmountZfbTotal = number_format($hourAmountZfbTotal, 2);
        $todayAmountZfbTotal = number_format($todayAmountZfbTotal, 2);
        $yesterdayAmountZfbTotal = number_format($yesterdayAmountZfbTotal, 2);

        $hourAmountTotal = number_format($hourAmountTotal, 2);
        $todayAmountTotal = number_format($todayAmountTotal, 2);
        $yesterdayAmountTotal = number_format($yesterdayAmountTotal, 2);
        $currentPrepaid = number_format($currentPrepaid, 2);
        $prepaidBalance = number_format($prepaidBalance, 2);

        $message = urlencode("
<b>💰商户统计数据</b>
<b>———" . date('Y-m-d H:i:s', time()) . "———</b>
<b>商户名称：{$merchantName}</b>
<b>当前预付：{$currentPrepaid}</b>
<b>预付余额：{$prepaidBalance}</b>
<b>————————————</b>
<b>通道类型：微信</b>
<b>小时金额：" . $hourAmountWxTotal . "</b>
<b>今日金额：" . $todayAmountWxTotal . "</b>
<b>昨日金额：" . $yesterdayAmountWxTotal . "</b>
<b>————————————</b>
<b>通道类型：支付宝</b>
<b>小时金额：" . $hourAmountZfbTotal . "</b>
<b>今日金额：" . $todayAmountZfbTotal . "</b>
<b>昨日金额：" . $yesterdayAmountZfbTotal . "</b>
<b>————————————</b>
<b>通道类型：总计</b>
<b>小时金额：" . $hourAmountTotal . "</b>
<b>今日金额：" . $todayAmountTotal . "</b>
<b>昨日金额：" . $yesterdayAmountTotal . "</b>
<b>————————————</b>");
        $this->sendMessage($chatId, $message);
    }

    /**
     * 增加预付
     */
    protected function addPrepaid($chatId, $merchantId, $merchantName, $text)
    {
        $parts = explode(' ', $text);
        if (count($parts) < 2) {
            $this->sendMessage($chatId, "❌ 格式错误！请使用：/加预付 金额 [备注]");
            return;
        }

        $amount = floatval($parts[1]);
        if ($amount <= 0) {
            $this->sendMessage($chatId, "❌ 金额必须大于0！");
            return;
        }

        $remark = isset($parts[2]) ? $parts[2] : '手动增加预付';

        try {
            // 参考系统现有的预付功能实现
            $currentPrepaid = Prepaid::getCurrentPrepaid($merchantId);
            $newPrepaid = $currentPrepaid + $amount;

            // 保存预付记录
            $prepaidData = [
                'merchant_id' => $merchantId,
                'before_amount' => $currentPrepaid,
                'after_amount' => $newPrepaid,
                'change_amount' => $amount,
                'remark' => $remark,
                'createtime' => time(),
                'updatetime' => time()
            ];

            db('prepaid')->insert($prepaidData);

            $message = "✅ 预付增加成功！\n";
            $message .= "增加金额：" . number_format($amount, 2) . "\n";
            $message .= "当前预付：" . number_format($newPrepaid, 2) . "\n";
            $message .= "备注：{$remark}";

            $this->sendMessage($chatId, urlencode($message));
        } catch (\Exception $e) {
            $this->sendMessage($chatId, "❌ 操作失败：" . $e->getMessage());
        }
    }

    /**
     * 减少预付
     */
    protected function reducePrepaid($chatId, $merchantId, $merchantName, $text)
    {
        $parts = explode(' ', $text);
        if (count($parts) < 2) {
            $this->sendMessage($chatId, "❌ 格式错误！请使用：/减预付 金额 [备注]");
            return;
        }

        $amount = floatval($parts[1]);
        if ($amount <= 0) {
            $this->sendMessage($chatId, "❌ 金额必须大于0！");
            return;
        }

        $remark = isset($parts[2]) ? $parts[2] : '手动减少预付';

        try {
            // 参考系统现有的预付功能实现
            $currentPrepaid = Prepaid::getCurrentPrepaid($merchantId);
            $newPrepaid = $currentPrepaid - $amount;

            // 保存预付记录
            $prepaidData = [
                'merchant_id' => $merchantId,
                'before_amount' => $currentPrepaid,
                'after_amount' => $newPrepaid,
                'change_amount' => -$amount,
                'remark' => $remark,
                'createtime' => time(),
                'updatetime' => time()
            ];

            db('prepaid')->insert($prepaidData);

            $message = "✅ 预付减少成功！\n";
            $message .= "减少金额：" . number_format($amount, 2) . "\n";
            $message .= "当前预付：" . number_format($newPrepaid, 2) . "\n";
            $message .= "备注：{$remark}";

            $this->sendMessage($chatId, urlencode($message));
        } catch (\Exception $e) {
            $this->sendMessage($chatId, "❌ 操作失败：" . $e->getMessage());
        }
    }

    /**
     * 计算指定日期的总汇后金额
     * @param int $merchant_id 商户ID
     * @param string $date 日期格式 Y-m-d
     * @return float 总汇后金额
     */
    private function calculateDaySettled($merchant_id, $date)
    {
        // 获取指定日期的开始和结束时间
        $dayStart = strtotime($date . ' 00:00:00');
        $dayEnd = strtotime($date . ' 23:59:59');

        $totalSettled = 0;
        $paytypes = ['huya', 'yahu'];

        foreach ($paytypes as $paytype) {
            // 查询主表
            $mainSettled = db('order')
                ->where('merchant_id', $merchant_id)
                ->where('paytype', $paytype)
                ->where('pay_status', 1)
                ->whereTime('callback_time', 'between', [$dayStart, $dayEnd])
                ->sum('amount');

            // 查询归档表
            $archiveSettled = db('order_history')
                ->where('merchant_id', $merchant_id)
                ->where('paytype', $paytype)
                ->where('pay_status', 1)
                ->whereTime('callback_time', 'between', [$dayStart, $dayEnd])
                ->sum('amount');

            $totalSettled += $mainSettled + $archiveSettled;
        }

        return $totalSettled;
    }

    /**
     * 发送帮助信息
     */
    protected function sendHelpMessage($chatId)
    {
        $message = "🤖 机器人使用帮助\n";
        $message .= "————————————\n";
        $message .= "/查账 - 查看商户统计数据\n";
        $message .= "/加预付 金额 [备注] - 增加预付\n";
        $message .= "/减预付 金额 [备注] - 减少预付\n";
        $message .= "/id - 查看群组和商户信息\n";
        $message .= "/帮助 - 显示此帮助信息\n";
        $message .= "————————————\n";
        $message .= "示例：\n";
        $message .= "/加预付 1000 充值\n";
        $message .= "/减预付 500 结算";

        $this->sendMessage($chatId, urlencode($message));
    }

    protected function sendMessage($chatId, $message)
    {
        $url = "https://api.telegram.org/bot" . $this->botToken . "/sendMessage?chat_id=$chatId&parse_mode=HTML&text=$message";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);
    }
}
