<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;

class Test extends Frontend
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        return $this->view->fetch();
    }

    /**
     * 生成与fa_order表结构一致的模拟订单数据
     * @return \think\response\Json
     */
    public function generateMockOrder()
    {
        $now = time();
        $order = [
            'out_trade_no'    => 'OUT' . rand(********, ********), // 商户订单号
            'local_trade_no'  => 'LOC' . rand(********, ********), // 本站订单号
            'pay_trade_no'    => 'PAY' . rand(********, ********), // 支付订单号
            'amount'          => round(rand(100, 10000) / 100, 2), // 订单金额，1.00~100.00
            'merchant_id'     => rand(2, 3),                    // 上游商户id
            'create_ip'       => '192.168.' . rand(0, 255) . '.' . rand(1, 254), // 拉单ip
            'useragent'       => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', // 浏览器头
            'paytype'         => 'yahu',                         // 通道编码
            'pay_url'         => 'https://pay.example.com/' . rand(1000, 9999), // 支付地址
            'pay_status'      => rand(0, 1),                        // 支付状态
            'callback_url'    => 'https://callback.example.com',   // 回调地址
            'callback_status' => rand(0, 1),                        // 回调状态
            'callback_time'   => $now + rand(60, 600),              // 回调时间
            'create_time'     => $now,                             // 创建时间
            'update_time'     => $now + rand(60, 600),              // 更新时间
            'remark'          => '测试订单',                        // 备注
            'account_id'      => 26,                     // 账号
            'user_id'         => 2,                     // 创建人ID
            'dy_order_id'     => 'DY' . rand(********, ********),  // 支付订单号
            'papi_id'         => 'PAPI' . rand(100000, 999999),    // 支付订单号
        ];
        return json($order);
    }

    /**
     * 批量生成并插入模拟订单数据
     * @return \think\response\Json
     */
    public function batchInsertMockOrders()
    {
        $now = time();
        $dataList = [];
        for ($i = 0; $i < 1000; $i++) {
            $paytypeArr = ['yahu', 'huya'];
            // 生成近3天内的随机时间戳
            $randTime = rand(time() - 3 * 86400, time());
            $status = rand(0, 1);
            $dataList[] = [
                'out_trade_no'    => 'OUT' . rand(********, ********),
                'local_trade_no'  => 'LOC' . rand(********, ********),
                'pay_trade_no'    => 'PAY' . rand(********, ********),
                'amount'          => round(rand(100, 10000) / 100, 2),
                'merchant_id'     => rand(2, 3),
                'create_ip'       => '192.168.' . rand(0, 255) . '.' . rand(1, 254),
                'useragent'       => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'paytype'         => $paytypeArr[array_rand($paytypeArr)],
                'pay_url'         => 'https://pay.example.com/' . rand(1000, 9999),
                'pay_status'      => $status,
                'callback_url'    => 'https://callback.example.com',
                'callback_status' => $status,
                // 回调时间、创建时间、更新时间都基于近3天内的随机时间
                'callback_time'   => $randTime + rand(60, 600),
                'create_time'     => $randTime,
                'update_time'     => $randTime + rand(60, 600),
                'remark'          => '测试订单',
                'account_id'      => 26,
                'user_id'         => 2,
                'dy_order_id'     => 'DY' . rand(********, ********),
                'papi_id'         => 'PAPI' . rand(100000, 999999),
            ];
        }
        // 批量插入数据
        $result = \think\Db::name('order')->insertAll($dataList);
        return json([
            'inserted_count' => $result,
            'data' => $dataList
        ]);
    }
}
