<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付款页面</title>
    <!-- 禁用referer -->
    <meta name="referrer" content="no-referrer">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 0;
            padding: 40px; /* 增加内边距 */
            background-color: #f8f9fa; /* 更柔和的背景颜色 */
        }

        img {
            height: 60px; /* 增加Logo高度 */
            margin-bottom: 20px;
        }

        .amount {
            font-size: 32px; /* 更大字体 */
            margin: 20px 0; /* 增加上下间距 */
            font-weight: bold;
            color: #333; /* 深色文字 */
        }

        .warning {
            color: #dc3545; /* 红色 */
            margin: 15px 0; /* 增加上下间距 */
            font-size: 18px; /* 增加提示文本的字体大小 */
        }

        .timer {
            margin: 15px 0; /* 增加上下间距 */
            font-size: 24px; /* 更大字体 */
            font-weight: bold;
            color: #dc3545; /* 红色 */
        }

        .details {
            margin-top: 30px; /* 增加上方间距 */
            background-color: #ffffff;
            border-radius: 10px; /* 圆角 */
            padding: 25px; /* 增加内边距 */
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1); /* 更明显的阴影 */
        }

        .details p {
            margin: 10px 0; /* 段落间距 */
            font-size: 18px; /* 调整字体大小 */
            color: #555; /* 深灰色 */
            display: flex; /* 使用弹性布局 */
            justify-content: space-between; /* 内容左右两端对齐 */
            align-items: center; /* 垂直居中对齐 */
        }

        .details p span {
            text-align: right; /* 右侧内容右对齐 */
        }

        .pay-button {
            margin-top: 30px; /* 增加上方间距 */
            padding: 14px 28px; /* 按钮更大内边距 */
            background-color: #007bff; /* 蓝色背景 */
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 20px; /* 增大字体 */
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s; /* 添加变化效果 */
        }

        .pay-button:hover {
            background-color: #0056b3; /* 鼠标悬停效果 */
            transform: translateY(-2px); /* 增加提升效果 */
        }

        /* 遮罩层样式 */
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .overlay-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .overlay-content img {
            width: 200px;
            height: auto;
            margin-bottom: 20px;
        }

        .overlay-button {
            margin-top: 20px;
            padding: 12px 24px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .overlay-button:hover {
            background-color: #218838;
        }

        /* 自定义提示框样式 */
        .toast-notification {
            visibility: hidden;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            text-align: center;
            border-radius: 8px;
            padding: 16px;
            z-index: 2000;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
            font-size: 18px;
            min-width: 250px;
        }

        .toast-notification.show {
            visibility: visible;
            opacity: 1;
        }

        .toast-notification.error {
            background-color: #dc3545;
        }

        /* 手动复制订单号遮罩层样式 */
        .manual-copy-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 1500;
            justify-content: center;
            align-items: center;
        }

        .manual-copy-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .order-number-display {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 20px;
            font-weight: bold;
            user-select: all; /* 使文本可一键全选 */
        }

        .copy-instructions {
            margin: 15px 0;
            color: #555;
            font-size: 16px;
        }
    </style>
</head>

<body>
    <div class="amount">订单金额：¥ {$order.amount}</div>

    <div class="warning" id="warningText">
        正在请求支付链接，请耐心等待<br>
        请在下方倒计时结束前及时付款，超时请勿付款
    </div>
    <div class="timer" id="countdown">05:00</div>

    <div class="details">
        <p>商户订单号：<span id="orderNumber">{$order.out_trade_no}</span></p>
        <p>创建时间：<span id="orderCreateTime">{$order.create_time|date='Y-m-d H:i:s',###}</span></p>
        <p>订单状态：<span id="orderStatus">等待支付</span></p>
    </div>

    <button class="pay-button" id="payButton" disabled style="background-color: #aaaaaa; cursor: not-allowed;" onclick="handlePayment()">立即支付</button>

    <!-- 自定义提示框 -->
    <div class="toast-notification" id="toastNotification"></div>

    <script>
        // 支付链接变量
        let paymentUrl = '';
        let requestCount = 0;
        const MAX_RETRY = 300; // 最大重试次数（5分钟，每秒一次）
        
        // 从创建时间计算剩余时间（5分钟过期）
        function calculateRemainingTime() {
            // 获取订单创建时间
            const orderCreateTimeStr = document.getElementById('orderCreateTime').textContent;
            const orderCreateTime = new Date(orderCreateTimeStr.replace(/-/g, '/'));
            
            // 计算过期时间（创建时间 + 5分钟）
            const expiryTime = new Date(orderCreateTime.getTime() + 5 * 60 * 1000);
            
            // 计算当前时间与过期时间的差值（秒）
            const currentTime = new Date();
            const timeDiffSeconds = Math.floor((expiryTime - currentTime) / 1000);
            
            // 如果已过期，返回0
            return Math.max(0, timeDiffSeconds);
        }

        // 初始化剩余时间
        let timeLeft = calculateRemainingTime();
        const countdownElement = document.getElementById('countdown');

        const updateTimer = () => {
            let minutes = Math.floor(timeLeft / 60);
            let seconds = timeLeft % 60;
            countdownElement.textContent = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            if (timeLeft > 0) {
                timeLeft--;
            } else {
                clearInterval(timerInterval);
                countdownElement.textContent = "已过期";
                
                // 禁用支付按钮
                document.getElementById('payButton').disabled = true;
                document.getElementById('payButton').style.backgroundColor = '#aaaaaa';
                document.getElementById('payButton').style.cursor = 'not-allowed';
                document.getElementById('warningText').innerHTML = "订单已过期，请重新下单";
            }
        };

        // 显示自定义提示
        function showToast(message, isError = false) {
            const toast = document.getElementById('toastNotification');
            toast.textContent = message;
            
            // 根据类型设置不同样式
            if (isError) {
                toast.classList.add('error');
            } else {
                toast.classList.remove('error');
            }
            
            // 显示提示
            toast.classList.add('show');
            
            // 1.5秒后自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
            }, 1500);
        }
        
        // 处理支付按钮点击
        function handlePayment() {
            if (paymentUrl) {
                window.location.href = paymentUrl;
            } else {
                showToast('支付链接正在获取中，请稍候...', true);
            }
        }
        
        // 请求支付链接的函数
        function requestPaymentUrl() {
            if (timeLeft <= 0 || requestCount >= MAX_RETRY) {
                document.getElementById('warningText').innerHTML = "支付链接请求超时，请刷新页面重试";
                return;
            }
            
            const orderNumber = document.getElementById('orderNumber').textContent;
            
            // 发起AJAX请求获取支付链接
            fetch('/index/cashier/getPayInfo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'out_trade_no=' + encodeURIComponent(orderNumber)
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 1 && data.url) {
                    // 获取到支付链接
                    paymentUrl = data.url;
                    
                    // 更新UI
                    document.getElementById('warningText').innerHTML = "支付链接已就绪，请点击下方按钮进行支付";
                    document.getElementById('orderStatus').textContent = "支付链接已就绪";
                    
                    // 激活支付按钮
                    const payButton = document.getElementById('payButton');
                    payButton.disabled = false;
                    payButton.style.backgroundColor = '#007bff';
                    payButton.style.cursor = 'pointer';
                    
                    // 显示成功提示
                    showToast('支付链接已就绪');
                    
                    // 自动跳转到支付页面
                    window.location.href = paymentUrl;
                    
                } else if (data.code === 0 && data.msg) {
                    // 接口返回失败，增加重试次数
                    requestCount++;
                    document.getElementById('warningText').innerHTML = "正在请求支付链接，请耐心等待<br>请在下方倒计时结束前及时付款，超时请勿付款";
                    
                    // 延迟1秒后重新请求
                    setTimeout(requestPaymentUrl, 1000);
                }
            })
            .catch(error => {
                // 请求失败，增加重试次数
                requestCount++;
                console.error('请求支付链接时出错:', error);
                document.getElementById('warningText').innerHTML = "正在请求支付链接，请耐心等待<br>请在下方倒计时结束前及时付款，超时请勿付款";
                
                // 延迟1秒后重新请求
                setTimeout(requestPaymentUrl, 1000);
            });
        }
        
        // 立即执行一次更新，以显示正确的初始时间
        updateTimer();
        
        // 设置定时器，每秒更新一次
        const timerInterval = setInterval(updateTimer, 1000);
        
        // 页面加载后立即请求一次支付链接
        document.addEventListener('DOMContentLoaded', function() {
            requestPaymentUrl();
        });
    </script>
</body>

</html>
