<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>在线收银台</title>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            text-align: center;
            padding: 15px 0;
            border-bottom: 1px solid #eaeaea;
            margin-bottom: 20px;
        }

        .header h1 {
            font-size: 24px;
            color: #333;
            font-weight: 500;
        }

        .header .logo {
            max-height: 60px;
            margin-bottom: 10px;
        }

        /* 充值表单样式 */
        .recharge-form {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .recharge-form h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 18px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            border-color: #1890ff;
            outline: none;
        }

        .form-group .input-prefix {
            position: relative;
        }

        .form-group .input-prefix input {
            padding-left: 30px;
        }

        .form-group .input-prefix::before {
            content: '￥';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 18px;
        }

        .quick-amount {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .quick-amount button {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
            flex-grow: 1;
            min-width: 80px;
        }

        .quick-amount button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .quick-amount button.active {
            border-color: #1890ff;
            background: #f0f9ff;
            color: #1890ff;
        }

        /* 支付方式样式 */
        .payment-methods {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .payment-methods h2 {
            font-size: 18px;
            margin-bottom: 15px;
            color: #333;
            font-weight: 500;
        }

        .payment-option {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #eaeaea;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-option:hover,
        .payment-option.active {
            border-color: #1890ff;
            background-color: #f0f9ff;
        }

        .payment-option.active {
            border-color: #1890ff;
            background-color: #f0f9ff;
        }

        .payment-logo {
            width: 40px;
            height: 40px;
            margin-right: 15px;
        }

        .payment-details {
            flex: 1;
        }

        .payment-name {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .payment-description {
            font-size: 14px;
            color: #888;
        }

        .payment-radio {
            width: 20px;
            height: 20px;
        }

        /* 底部确认区域 */
        .payment-confirm {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .payment-notice {
            margin-bottom: 15px;
            font-size: 14px;
            color: #666;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
        }

        .payment-notice p {
            margin-bottom: 5px;
        }

        .btn-pay {
            width: 100%;
            padding: 14px 20px;
            background-color: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .btn-pay:hover {
            background-color: #0056b3;
        }

        .btn-pay:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        /* 响应式调整 */
        @media (max-width: 576px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 20px;
            }

            .payment-option {
                padding: 10px;
            }

            .payment-logo {
                width: 30px;
                height: 30px;
            }

            .payment-name {
                font-size: 14px;
            }

            .payment-description {
                font-size: 12px;
            }

            .quick-amount {
                gap: 8px;
            }

            .quick-amount button {
                padding: 8px 12px;
                font-size: 14px;
            }
        }

        .form-group select {
            width: 100%;
            padding: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 18px;
            transition: border-color 0.3s;
            background-color: #fff;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.763L10.825 4z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 15px center;
            padding-right: 40px;
        }

        .form-group select:focus {
            border-color: #1890ff;
            outline: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>在线收银台</h1>
        </div>

        <form action="{:url('cashier/createOrder')}" method="post" id="rechargeForm">
            <div class="recharge-form">
                <h2>请输入充值金额</h2>
                <div class="form-group">
                    <label>选择商户</label>
                    <select name="merchant_id" id="merchant_id" required class="form-control">
                        <option value="">请选择商户</option>
                        {volist name="merchants" id="merchant"}
                        <option value="{$merchant.id}">{$merchant.name}</option>
                        {/volist}
                    </select>
                </div>
                
                <!-- 新增通道选择下拉框 -->
                <div class="form-group">
                    <label>选择支付通道</label>
                    <select name="channel" id="channel" required class="form-control">
                        <option value="">请选择支付通道</option>
                        <option value="huya">虎牙</option>
                        <option value="yahu">雅虎</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="input-prefix">
                        <input type="number" name="amount" id="amount" placeholder="请输入金额" required min="0" step="0.01">
                    </div>
                    <div class="quick-amount">
                        <button type="button" class="amount-btn" data-amount="10">10元</button>
                        <button type="button" class="amount-btn" data-amount="20">20元</button>
                        <button type="button" class="amount-btn" data-amount="30">30元</button>
                        <button type="button" class="amount-btn" data-amount="50">50元</button>
                        <button type="button" class="amount-btn" data-amount="100">100元</button>
                        <button type="button" class="amount-btn" data-amount="200">200元</button>
                        <button type="button" class="amount-btn" data-amount="500">500元</button>
                        <button type="button" class="amount-btn" data-amount="1000">1000元</button>
                        <button type="button" class="amount-btn" data-amount="2000">2000元</button>
                        <button type="button" class="amount-btn" data-amount="3000">3000元</button>
                        <button type="button" class="amount-btn" data-amount="5000">5000元</button>
                        <button type="button" class="amount-btn" data-amount="10000">10000元</button>
                        <button type="button" class="amount-btn" data-amount="20000">20000元</button>
                        <button type="button" class="amount-btn" data-amount="50000">50000元</button>
                        <button type="button" class="amount-btn" data-amount="100000">100000元</button>
                        <button type="button" class="amount-btn" data-amount="200000">200000元</button>
                        <button type="button" class="amount-btn" data-amount="500000">500000元</button>
                        <button type="button" class="amount-btn" data-amount="1000000">1000000元</button>
                    </div>
                </div>
            </div>

            <div class="payment-confirm">
                <div class="payment-notice">
                    <p>温馨提示：</p>
                    <p>1. 请核对金额后再提交支付</p>
                    <p>2. 付款成功后，系统会自动为您处理</p>
                    <p>3. 如有问题，请联系客服</p>
                </div>
                <button type="submit" class="btn-pay" id="btnPay">确认支付</button>
            </div>
        </form>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 支付方式选择
            const paymentOptions = document.querySelectorAll('.payment-option');
            paymentOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除所有选中状态
                    paymentOptions.forEach(o => o.classList.remove('active'));
                    
                    // 添加当前选中状态
                    this.classList.add('active');
                    
                    // 选中对应的单选按钮
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;
                });
            });
            
            // 快速金额选择
            const amountButtons = document.querySelectorAll('.amount-btn');
            const amountInput = document.getElementById('amount');
            
            amountButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有选中状态
                    amountButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // 添加当前选中状态
                    this.classList.add('active');
                    
                    // 设置输入框的值
                    amountInput.value = this.dataset.amount;
                });
            });
            
            // 表单提交前验证
            document.getElementById('rechargeForm').addEventListener('submit', function(e) {
                const amount = amountInput.value;
                const merchantId = document.getElementById('merchant_id').value;
                const channel = document.getElementById('channel').value;
                
                if (!merchantId) {
                    e.preventDefault();
                    alert('请选择商户');
                    return;
                }
                
                if (!channel) {
                    e.preventDefault();
                    alert('请选择支付通道');
                    return;
                }
                
                if (!amount || amount <= 0) {
                    e.preventDefault();
                    alert('请输入正确的金额');
                    return;
                }
                
                // 获取选中的支付方式
                const selectedPayment = document.querySelector('input[name="paymentMethod"]:checked').value;
                
                // 根据不同的支付方式处理
                if (selectedPayment === 'wechat') {
                    e.preventDefault();
                    alert('微信支付功能即将上线，敬请期待！');
                    return;
                } else if (selectedPayment === 'bank') {
                    e.preventDefault();
                    alert('银行卡支付功能即将上线，敬请期待！');
                    return;
                }
            });

            // 自动聚焦到金额输入框
            amountInput.focus();
        });
    </script>
</body>

</html>
