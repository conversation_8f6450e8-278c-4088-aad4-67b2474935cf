# API接口文档

## 1. 接口概览

### 基础信息
- **API版本**: v1.0
- **基础URL**: `https://your-domain.com/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **签名算法**: MD5

### 通用响应格式
```json
{
    "code": 1,           // 状态码：1成功，0失败
    "msg": "success",    // 响应消息
    "data": {}          // 响应数据（可选）
}
```

## 2. 支付网关接口

### 2.1 创建支付订单
**接口地址**: `POST /api/gateway/index`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号，全局唯一 |
| mid | int | 是 | 商户ID |
| amount | decimal | 是 | 订单金额，范围0.01-50000.00 |
| channel_code | string | 是 | 支付渠道代码(Wx-Wap/Zfb-Wap) |
| notify_url | string | 是 | 异步通知地址 |
| callback_url | string | 否 | 同步回调地址 |
| sign | string | 是 | 签名字符串 |

**签名算法**:
```php
// 1. 参数按字典序排序（除sign外）
// 2. 拼接成 key1=value1&key2=value2 格式
// 3. 末尾追加 &key=商户密钥
// 4. 进行MD5加密
$sign = md5(urldecode($paramStr) . "&key=" . $merchantKey);
```

**请求示例**:
```json
{
    "out_trade_no": "**********789",
    "mid": 1,
    "amount": "100.00",
    "channel_code": "Wx-Wap",
    "notify_url": "https://merchant.com/notify",
    "callback_url": "https://merchant.com/callback",
    "sign": "abc123def456..."
}
```

**成功响应**:
```json
{
    "code": 1,
    "msg": "订单创建成功",
    "data": {
        "url": "https://pay.domain.com/pay?orderno=**********789",
        "order_id": "SYS**********789",
        "amount": "100.00"
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "签名验证失败"
}
```

### 2.2 订单状态查询
**接口地址**: `POST /api/gateway/query`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号 |
| mid | int | 是 | 商户ID |
| sign | string | 是 | 签名字符串 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "out_trade_no": "**********789",
        "pay_trade_no": "***************",
        "amount": "100.00",
        "real_amount": "100.00",
        "pay_status": 1,
        "pay_time": **********,
        "create_time": **********
    }
}
```

## 3. 账号管理接口

### 3.1 获取登录二维码
**接口地址**: `GET /api/account/getQeid`

**请求参数**: 无

**成功响应**:
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "qeid": "abc123def456",
        "qr_url": "https://qr.huya.com/abc123def456",
        "expires_in": 300
    }
}
```

### 3.2 检查登录状态
**接口地址**: `POST /api/account/tryQrcode`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qeid | string | 是 | 二维码ID |

**成功响应**:
```json
{
    "code": 1,
    "msg": "登录成功",
    "data": {
        "status": "success",
        "username": "用户名",
        "nickname": "昵称",
        "cookie": "登录cookie信息"
    }
}
```

### 3.3 添加收款账号
**接口地址**: `POST /api/account/add`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| username | string | 是 | 账号用户名 |
| nickname | string | 否 | 账号昵称 |
| config | string | 是 | 账号配置(JSON格式) |

**成功响应**:
```json
{
    "code": 1,
    "msg": "添加成功",
    "data": {
        "account_id": 123
    }
}
```

### 3.4 账号列表查询
**接口地址**: `GET /api/account/index`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| user_id | int | 否 | 用户ID筛选 |
| status | int | 否 | 状态筛选 |
| minDiamond | float | 否 | 最小余额筛选 |
| maxDiamond | float | 否 | 最大余额筛选 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "total": 100,
        "page": 1,
        "limit": 10,
        "list": [
            {
                "id": 1,
                "username": "test001",
                "nickname": "测试账号",
                "status": 1,
                "paystatus": 1,
                "balance": "1000.00",
                "total_amount": "5000.00",
                "order_count": 50,
                "success_rate": "95.00",
                "pulltime": **********,
                "create_time": **********
            }
        ]
    }
}
```

### 3.5 刷新账号余额
**接口地址**: `POST /api/account/refreshDiamond`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 账号ID |

**成功响应**:
```json
{
    "code": 1,
    "msg": "刷新成功",
    "data": {
        "diamond": 1000.50
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "账号登录已过期，请重新登录",
    "data": null
}
```

**功能说明**:
- 自动调用虎牙API获取账号最新余额
- 更新账号的钻石数量和拉取时间
- 清除账号的错误信息
- 自动处理登录过期等异常情况
- 管理员可刷新所有账号，普通用户只能刷新自己的账号

### 3.6 设置账号权重
**接口地址**: `POST /api/account/setWeight`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 账号ID |
| weight | int | 是 | 权重值（非负整数） |

**成功响应**:
```json
{
    "code": 1,
    "msg": "设置账号权重成功",
    "data": {
        "weight": 10
    }
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "权重值不能为负数",
    "data": null
}
```

**功能说明**:
- 设置账号的权重值，用于排序
- 权重值必须为非负整数
- 权重高的账号在列表中优先显示
- 管理员可设置所有账号权重，普通用户只能设置自己的账号权重

## 4. 订单管理接口

### 4.1 订单列表查询
**接口地址**: `GET /api/order/index`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |
| merchant_id | int | 否 | 商户ID筛选 |
| pay_status | int | 否 | 支付状态筛选 |
| start_time | int | 否 | 开始时间 |
| end_time | int | 否 | 结束时间 |
| out_trade_no | string | 否 | 商户订单号 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "total": 1000,
        "page": 1,
        "limit": 10,
        "list": [
            {
                "id": 1,
                "out_trade_no": "**********789",
                "pay_trade_no": "***************",
                "merchant_name": "测试商户",
                "amount": "100.00",
                "real_amount": "100.00",
                "paytype": "huya",
                "pay_status": 1,
                "callback_status": 1,
                "create_time": **********,
                "pay_time": **********
            }
        ]
    }
}
```

### 4.2 订单回调状态查询
**接口地址**: `POST /api/order/callback`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "查询成功",
    "data": {
        "pay_status": 1,
        "callback_status": 1,
        "callback_retry_count": 0,
        "last_callback_time": **********
    }
}
```

### 4.3 订单数据导出
**接口地址**: `POST /api/order/export`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_time | int | 是 | 开始时间 |
| end_time | int | 是 | 结束时间 |
| merchant_id | int | 否 | 商户ID筛选 |
| pay_status | int | 否 | 支付状态筛选 |

**成功响应**:
```json
{
    "code": 1,
    "msg": "导出成功",
    "data": {
        "download_url": "https://domain.com/uploads/export_20231221.xlsx",
        "file_size": "2.5MB",
        "record_count": 1000
    }
}
```

## 5. 收银台接口

### 5.1 获取支付信息
**接口地址**: `POST /index/cashier/getPayInfo`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| out_trade_no | string | 是 | 商户订单号 |
| ip | string | 是 | 客户端IP |

**成功响应**:
```json
{
    "code": 1,
    "msg": "获取支付链接成功",
    "url": "https://pay.huya.com/xxx"
}
```

**错误响应**:
```json
{
    "code": 0,
    "msg": "订单不存在"
}
```

## 6. 错误码定义

### 6.1 通用错误码
| 错误码 | 说明 |
|--------|------|
| 0 | 失败 |
| 1 | 成功 |
| -1 | 系统异常 |
| -2 | 参数错误 |
| -3 | 签名验证失败 |
| -4 | 权限不足 |
| -5 | 频率限制 |

### 6.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 商户不存在 |
| 1002 | 商户已禁用 |
| 1003 | 订单已存在 |
| 1004 | 订单不存在 |
| 1005 | 金额超出限制 |
| 1006 | 支付渠道不支持 |
| 1007 | 账号不可用 |
| 1008 | 代理IP不可用 |
| 1009 | 拉单失败 |
| 1010 | 订单已支付 |

## 7. 签名验证

### 7.1 签名生成规则
1. 将所有请求参数（除sign外）按参数名字典序排序
2. 将排序后的参数拼接成 `key1=value1&key2=value2` 格式
3. 在末尾追加商户密钥：`&key=merchant_key`
4. 对整个字符串进行MD5加密得到签名

### 7.2 PHP签名示例
```php
function generateSign($params, $merchantKey) {
    // 移除sign参数
    unset($params['sign']);
    
    // 按key排序
    ksort($params);
    
    // 拼接参数
    $paramStr = http_build_query($params);
    
    // 追加密钥并MD5加密
    return md5(urldecode($paramStr) . "&key=" . $merchantKey);
}
```

### 7.3 验证流程
1. 接收请求参数
2. 提取sign参数
3. 按相同规则生成签名
4. 比较生成的签名与接收的签名
5. 签名一致则验证通过

## 8. 接口调用示例

### 8.1 创建订单完整示例
```php
<?php
$params = [
    'out_trade_no' => time(),
    'mid' => 1,
    'amount' => '100.00',
    'channel_code' => 'Wx-Wap',
    'notify_url' => 'https://merchant.com/notify'
];

$merchantKey = 'your_merchant_key';
$params['sign'] = generateSign($params, $merchantKey);

$response = httpPost('https://domain.com/api/gateway/index', $params);
$result = json_decode($response, true);

if ($result['code'] == 1) {
    // 跳转到支付页面
    header('Location: ' . $result['data']['url']);
} else {
    echo '创建订单失败：' . $result['msg'];
}
?>
```

### 8.2 查询订单状态示例
```php
<?php
$params = [
    'out_trade_no' => '**********789',
    'mid' => 1
];

$merchantKey = 'your_merchant_key';
$params['sign'] = generateSign($params, $merchantKey);

$response = httpPost('https://domain.com/api/gateway/query', $params);
$result = json_decode($response, true);

if ($result['code'] == 1) {
    $orderInfo = $result['data'];
    echo '支付状态：' . ($orderInfo['pay_status'] ? '已支付' : '未支付');
} else {
    echo '查询失败：' . $result['msg'];
}
?>
```

## 9. 异步通知

### 9.1 通知格式
商户在创建订单时提供的 `notify_url` 将接收到以下格式的POST通知：

```json
{
    "out_trade_no": "**********789",
    "pay_trade_no": "***************",
    "amount": "100.00",
    "real_amount": "100.00",
    "pay_status": 1,
    "pay_time": **********,
    "sign": "abc123def456..."
}
```

### 9.2 通知验证
商户需要验证通知的签名，确保通知来源的真实性。

### 9.3 响应要求
商户处理完通知后，需要返回字符串 `"success"`，否则系统将进行重试。

### 9.4 重试机制
- 重试次数：最多5次
- 重试间隔：1分钟、5分钟、15分钟、30分钟、60分钟
- 超时时间：30秒

## 10. 接口限流

### 10.1 限流规则
- 创建订单：每分钟最多100次
- 查询接口：每分钟最多1000次
- 登录相关：每分钟最多10次

### 10.2 限流响应
```json
{
    "code": -5,
    "msg": "请求过于频繁，请稍后重试"
}
```