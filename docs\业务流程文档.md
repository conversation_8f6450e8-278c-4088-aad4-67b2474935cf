# 业务流程文档

## 1. 系统概述

### 1.1 系统架构
本系统是一个基于虎牙平台的支付网关系统，主要功能包括：
- 支付订单创建与处理
- 虎牙账号管理与登录
- 支付状态监控与回调
- 商户管理与结算
- 数据统计与报表

### 1.2 核心角色
- **商户**: 接入支付服务的第三方商家
- **用户**: 系统内部用户，管理收款账号
- **管理员**: 系统管理员，负责整体运营
- **收款账号**: 虎牙平台的实际收款账号

## 2. 完整支付流程

### 2.1 支付流程时序图
```mermaid
sequenceDiagram
    participant C as 客户
    participant M as 商户
    participant G as 支付网关
    participant CS as 收银台
    participant H as 虎牙API
    participant A as 收款账号

    C->>M: 1.发起支付请求
    M->>G: 2.调用创建订单API
    G->>G: 3.验证商户和签名
    G->>G: 4.创建本地订单
    G->>CS: 5.返回收银台链接
    M->>C: 6.重定向到收银台
    
    C->>CS: 7.访问收银台页面
    CS->>CS: 8.获取订单信息
    CS->>CS: 9.检查是否有支付链接
    
    alt 无支付链接
        CS->>CS: 10.获取缓存锁
        CS->>CS: 11.选择可用账号
        CS->>CS: 12.分配代理IP
        CS->>H: 13.调用虎牙充值API
        H->>A: 14.创建虎牙订单
        A-->>H: 15.返回支付链接
        H-->>CS: 16.返回支付信息
        CS->>CS: 17.更新订单支付链接
        CS->>CS: 18.释放缓存锁
    end
    
    CS->>C: 19.返回支付链接
    C->>H: 20.访问虎牙支付页面
    C->>H: 21.完成支付
    H->>A: 22.支付成功通知
    
    CS->>H: 23.轮询支付状态
    H-->>CS: 24.返回支付状态
    CS->>G: 25.更新订单状态
    G->>M: 26.发送异步通知
    M-->>G: 27.确认接收通知
```

### 2.2 详细流程步骤

#### 阶段一：订单创建（商户侧）
1. **客户发起支付**: 客户在商户网站选择商品并发起支付
2. **商户调用API**: 商户调用支付网关的创建订单接口
3. **参数验证**: 系统验证商户身份、签名、金额等参数
4. **订单入库**: 创建本地订单记录，状态为"未支付"
5. **返回收银台**: 返回收银台页面链接给商户
6. **页面跳转**: 商户将客户重定向到收银台页面

#### 阶段二：支付链接获取（收银台侧）
1. **页面加载**: 客户访问收银台页面
2. **订单查询**: 根据订单号查询订单信息
3. **链接检查**: 检查订单是否已有支付链接
4. **并发控制**: 使用缓存锁防止重复处理
5. **账号选择**: 智能选择可用的收款账号
6. **代理分配**: 根据客户IP分配合适的代理
7. **虎牙拉单**: 调用虎牙API创建支付订单
8. **链接更新**: 将获取的支付链接更新到订单
9. **页面跳转**: 将客户重定向到虎牙支付页面

#### 阶段三：支付处理（虎牙侧）
1. **支付页面**: 客户在虎牙页面完成支付操作
2. **支付确认**: 虎牙处理支付并更新订单状态
3. **状态同步**: 虎牙将支付结果同步到收款账号

#### 阶段四：状态监控（系统侧）
1. **状态轮询**: 收银台定时查询支付状态
2. **状态更新**: 发现支付成功后更新本地订单
3. **异步通知**: 向商户发送支付成功通知
4. **重试机制**: 通知失败时进行重试

## 3. 账号管理流程

### 3.1 账号登录流程
```mermaid
flowchart TD
    A[获取二维码] --> B[显示二维码]
    B --> C[用户扫码]
    C --> D[轮询登录状态]
    D --> E{登录成功?}
    E -->|否| F[继续轮询]
    E -->|是| G[获取Cookie]
    G --> H[保存账号信息]
    H --> I[账号可用]
    F --> D
```

#### 详细步骤：
1. **获取二维码**: 调用虎牙API获取登录二维码ID
2. **显示二维码**: 在管理页面显示二维码供用户扫描
3. **状态轮询**: 前端定时查询登录状态
4. **登录确认**: 用户扫码确认登录
5. **Cookie获取**: 获取登录后的Cookie信息
6. **账号保存**: 将账号信息和配置保存到数据库
7. **状态更新**: 将账号状态设置为可用

### 3.2 账号选择策略
```mermaid
flowchart TD
    A[开始选择账号] --> B{轮询模式?}
    B -->|是| C[按拉单时间升序]
    B -->|否| D[按ID降序]
    C --> E[过滤异常账号]
    D --> E
    E --> F[过滤频繁操作账号]
    F --> G[过滤未登录账号]
    G --> H[过滤违规账号]
    H --> I[过滤风险账号]
    I --> J{有可用账号?}
    J -->|是| K[返回第一个账号]
    J -->|否| L[返回无可用账号]
```

#### 过滤规则：
- **异常账号**: errormsg包含"异常"
- **频繁操作**: errormsg包含"频繁"
- **未登录**: errormsg包含"登录"
- **违规账号**: errormsg包含"违规"
- **风险账号**: errormsg包含"风险"
- **状态检查**: status=1（启用状态）

## 4. 订单状态流转

### 4.1 订单状态图
```mermaid
stateDiagram-v2
    [*] --> 创建订单
    创建订单 --> 获取支付链接: 拉单成功
    创建订单 --> 拉单失败: 拉单失败
    获取支付链接 --> 支付中: 用户访问支付页面
    支付中 --> 支付成功: 用户完成支付
    支付中 --> 支付超时: 超过5分钟
    支付成功 --> 通知成功: 商户确认接收
    支付成功 --> 通知失败: 商户未确认
    通知失败 --> 通知成功: 重试成功
    通知失败 --> 通知放弃: 重试5次失败
    拉单失败 --> [*]
    支付超时 --> [*]
    通知成功 --> [*]
    通知放弃 --> [*]
```

### 4.2 状态字段说明
| 字段 | 值 | 说明 | 业务含义 |
|------|----|----|---------|
| pay_status | 0 | 未支付 | 订单创建但未完成支付 |
| pay_status | 1 | 已支付 | 用户已完成支付 |
| callback_status | 0 | 未回调 | 尚未发送通知给商户 |
| callback_status | 1 | 回调成功 | 商户已确认接收通知 |
| callback_status | 2 | 回调失败 | 商户未确认或通知失败 |

## 5. 异常处理流程

### 5.1 拉单失败处理
```mermaid
flowchart TD
    A[拉单失败] --> B[记录错误日志]
    B --> C[更新账号错误信息]
    C --> D{错误类型判断}
    D -->|登录失效| E[标记账号需重新登录]
    D -->|频繁操作| F[标记账号暂时不可用]
    D -->|违规风险| G[标记账号高风险]
    D -->|网络异常| H[保持账号状态]
    E --> I[返回错误给用户]
    F --> I
    G --> I
    H --> I
```

### 5.2 支付超时处理
```mermaid
flowchart TD
    A[检测支付超时] --> B[标记订单超时]
    B --> C[释放占用资源]
    C --> D[记录超时日志]
    D --> E[统计超时率]
    E --> F[优化账号选择]
```

### 5.3 通知重试机制
```mermaid
flowchart TD
    A[发送通知] --> B{通知成功?}
    B -->|是| C[标记通知成功]
    B -->|否| D[重试次数+1]
    D --> E{重试次数<5?}
    E -->|是| F[等待重试间隔]
    E -->|否| G[标记通知放弃]
    F --> H[重新发送通知]
    H --> B
    C --> I[完成]
    G --> I
```

#### 重试间隔策略：
- 第1次重试：1分钟后
- 第2次重试：5分钟后
- 第3次重试：15分钟后
- 第4次重试：30分钟后
- 第5次重试：60分钟后

## 6. 并发控制机制

### 6.1 缓存锁机制
```php
// 获取订单锁
if (!CacheLock::acquireOrderLock($orderNo, 5)) {
    return '订单正在处理中，请稍后重试';
}

try {
    // 业务处理逻辑
    $result = processOrder($orderNo);
    return $result;
} finally {
    // 确保释放锁
    CacheLock::releaseOrderLock($orderNo);
}
```

### 6.2 双重检查模式
```php
// 第一次检查
if (!empty($order['pay_url'])) {
    return $order['pay_url'];
}

// 获取锁后再次检查
if (CacheLock::acquireOrderLock($orderNo)) {
    $order = getOrderFromDB($orderNo);
    if (!empty($order['pay_url'])) {
        return $order['pay_url'];  // 其他进程已处理
    }
    
    // 执行拉单逻辑
    $payUrl = createPayOrder($order);
    updateOrderPayUrl($orderNo, $payUrl);
    
    CacheLock::releaseOrderLock($orderNo);
    return $payUrl;
}
```

## 7. 业务规则

### 7.1 金额限制规则
- **最小金额**: 0.01元
- **最大金额**: 50000.00元
- **精度要求**: 保留2位小数
- **验证规则**: 必须为正数且在范围内

### 7.2 账号使用规则
- **轮询模式**: 按拉单时间升序选择账号
- **非轮询模式**: 按ID降序选择账号
- **冷却时间**: 账号拉单后需等待一定时间
- **错误处理**: 连续失败的账号暂时禁用

### 7.3 商户管理规则
- **费率设置**: 0.0000-1.0000之间
- **余额管理**: 支持预付费和后付费模式
- **IP白名单**: 限制访问来源IP
- **签名验证**: 所有请求必须验证签名

### 7.4 风控策略
- **频率限制**: 限制API调用频率
- **异常监控**: 监控异常订单和账号
- **风险评估**: 对高风险操作进行拦截
- **黑名单**: 维护商户和IP黑名单

## 8. 数据统计与监控

### 8.1 关键指标
- **订单成功率**: 成功订单数/总订单数
- **支付成功率**: 支付成功数/创建订单数
- **账号可用率**: 可用账号数/总账号数
- **平均响应时间**: API接口平均响应时间
- **异常率**: 异常订单数/总订单数

### 8.2 监控告警
- **订单积压**: 超过100个未处理订单
- **账号异常**: 可用账号少于10个
- **支付失败**: 成功率低于90%
- **系统异常**: 错误率超过5%
- **响应超时**: 平均响应时间超过3秒

### 8.3 日志记录
- **操作日志**: 记录所有关键操作
- **错误日志**: 记录所有异常和错误
- **性能日志**: 记录接口响应时间
- **业务日志**: 记录业务流程关键节点

## 9. 定时任务

### 9.1 订单归档任务
- **执行时间**: 每天凌晨2点
- **处理逻辑**: 将30天前的订单归档到历史表
- **批量大小**: 每批处理1000条
- **监控指标**: 归档数量和耗时

### 9.2 账号余额更新
- **执行频率**: 每小时执行一次
- **处理逻辑**: 更新所有账号的余额信息
- **并发控制**: 分批并发处理
- **异常处理**: 失败账号记录日志

### 9.3 数据清理任务
- **日志清理**: 清理90天前的日志
- **临时文件**: 清理过期的临时文件
- **缓存清理**: 清理过期的缓存数据

## 10. 应急处理预案

### 10.1 系统故障处理
1. **故障发现**: 监控系统发现异常
2. **影响评估**: 评估故障影响范围
3. **应急响应**: 启动应急处理流程
4. **故障修复**: 定位问题并修复
5. **服务恢复**: 恢复正常服务
6. **事后总结**: 分析原因并改进

### 10.2 数据恢复流程
1. **备份检查**: 确认备份数据完整性
2. **恢复策略**: 制定数据恢复方案
3. **恢复执行**: 执行数据恢复操作
4. **数据验证**: 验证恢复数据正确性
5. **服务重启**: 重启相关服务
6. **功能测试**: 测试系统功能正常

### 10.3 安全事件处理
1. **事件发现**: 发现安全异常
2. **紧急隔离**: 隔离受影响系统
3. **影响评估**: 评估安全影响
4. **修复加固**: 修复漏洞并加固
5. **监控加强**: 加强安全监控
6. **总结改进**: 总结经验并改进