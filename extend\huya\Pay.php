<?php

namespace huya;

class Pay
{
    /**
     * 创建订单
     * @param int $amount 充值金额
     * @param string $cookie 请求cookie
     * @param string $proxy 代理
     * @return array|false 返回请求结果或false
     */
    public static function createOrder($amount, $cookie, $chcode, $proxy)
    {
        if ($chcode == 'yahu') {
            $chcode = 'Zfb-Wap';
        } else {
            $chcode = 'Wx-Wap';
        }
        $res = Service::charge($amount, $cookie, $chcode, $proxy);
        if (!$res) {
            return ['success' => false, 'msg' => '代理连接失败或超时'];
        }
        if ($res['code'] != 0) {
            return ['success' => false, 'msg' => $res['message']];
        }
        return ['success' => true, 'data' => $res['data']];
    }

    /**
     * 创建订单
     * @param int $amount 充值金额
     * @param string $cookie 请求cookie
     * @param string $proxy 代理
     * @return array|false 返回请求结果或false
     */
    public static function createOrder2($amount, $cookie, $chcode, $proxy)
    {
        $res = Service::charge($amount, $cookie, $chcode, $proxy);
        if (!$res) {
            return ['success' => false, 'msg' => '代理连接失败或超时'];
        }
        $action = Service::payment_wap($res['data']['payUrl'], $proxy);
        if (!$action) {
            return ['success' => false, 'msg' => '提取微信支付地址失败'];
        }
        $deeplink = Service::getDeeplink($action, $proxy);
        if (!$deeplink) {
            return ['success' => false, 'msg' => '提取deeplink失败'];
        }
        $res['data']['payUrl'] = $deeplink;
        return ['success' => true, 'data' => $res['data']];
    }
}
