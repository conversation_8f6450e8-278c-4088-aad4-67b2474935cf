<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>丫丫支付网关API文档</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 10px;
        }
        h2 {
            color: #1a73e8;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 7px;
            margin-top: 30px;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .json {
            background-color: #f8f8f8;
        }
        .divider {
            border-top: 1px dashed #ccc;
            margin: 40px 0;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 4px solid #2196F3;
            padding: 10px 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>丫丫支付网关API文档</h1>

    <h2>创建订单接口</h2>

    <h3>接口描述</h3>
    <p>该接口用于接收商户的支付请求，验证参数后创建新的支付订单，并返回支付链接给商户。</p>

    <h3>请求信息</h3>
    <ul>
        <li><strong>接口路径</strong>: <code>/api/gateway</code></li>
        <li><strong>请求方法</strong>: POST</li>
        <li><strong>Content-Type</strong>: application/x-www-form-urlencoded</li>
    </ul>

    <h3>请求参数</h3>
    <table>
        <thead>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>mid</td>
                <td>int</td>
                <td>是</td>
                <td>商户ID</td>
            </tr>
            <tr>
                <td>out_trade_no</td>
                <td>string</td>
                <td>是</td>
                <td>商户订单号</td>
            </tr>
            <tr>
                <td>amount</td>
                <td>float</td>
                <td>是</td>
                <td>订单金额</td>
            </tr>
            <tr>
                <td>channel_code</td>
                <td>string</td>
                <td>是</td>
                <td>支付渠道代码，目前支持：huya</td>
            </tr>
            <tr>
                <td>notify_url</td>
                <td>string</td>
                <td>是</td>
                <td>支付结果通知回调地址</td>
            </tr>
            <tr>
                <td>sign</td>
                <td>string</td>
                <td>是</td>
                <td>请求参数签名</td>
            </tr>
        </tbody>
    </table>

    <h3>签名规则</h3>
    <ol>
        <li>
            <p>参与签名的字段：</p>
            <ul>
                <li>mid</li>
                <li>out_trade_no</li>
                <li>amount</li>
                <li>channel_code</li>
                <li>notify_url</li>
            </ul>
        </li>
        <li>
            <p>签名算法：</p>
            <ul>
                <li>将所有参与签名的参数按照参数名ASCII码从小到大排序</li>
                <li>将排序后的参数按照"参数名=参数值"格式拼接，并用"&"连接</li>
                <li>在拼接后的字符串末尾追加商户密钥："&key=商户密钥"</li>
                <li>对最终的字符串进行MD5加密（32位小写）</li>
            </ul>
        </li>
        <li>
            <p>签名示例：</p>
            <pre><code>// 原始参数
mid=10000
out_trade_no=TEST20231201001
amount=100.00
channel_code=douyin
notify_url=http://www.example.com/notify

// 按ASCII码排序并拼接
amount=100.00&channel_code=douyin&mid=10000&notify_url=http://www.example.com/notify&out_trade_no=TEST20231201001&key=1234567890

// MD5加密后得到签名
sign=62b8e7d5047e12f2b2c1fad1e6f5e6e7</code></pre>
        </li>
    </ol>

    <h3>响应参数</h3>
    <h4>成功响应</h4>
    <pre class="json"><code>{
    "code": 1,
    "msg": "创建成功",
    "data": {
        "url": "支付页面URL"
    }
}</code></pre>

    <h4>失败响应</h4>
    <pre class="json"><code>{
    "code": 0,
    "msg": "错误信息",
    "data": null
}</code></pre>

    <h3>错误码说明</h3>
    <p>以下情况会返回错误响应：</p>
    <ul>
        <li>参数验证失败</li>
        <li>商户不存在</li>
        <li>暂无可用服务商</li>
        <li>暂无可用账号</li>
        <li>订单号已存在</li>
        <li>不支持的支付渠道</li>
        <li>签名验证失败</li>
    </ul>

    <h3>注意事项</h3>
    <ol>
        <li>订单号必须唯一</li>
        <li>目前仅支持虎牙支付渠道</li>
        <li>确保回调通知地址可以正常访问</li>
        <li>订单金额必须大于0</li>
        <li>签名验证失败会导致请求被拒绝</li>
    </ol>

    <div class="divider"></div>

    <h2>回调说明</h2>
    <p>用于系统向商户发送支付结果通知，商户系统接收到通知后需要返回处理结果。</p>
    <h3>请求方式</h3>
    <ul>
        <li>请求方法：POST</li>
        <li>请求格式：application/x-www-form-urlencoded</li>
    </ul>

    <h3>请求参数</h3>
    <table>
        <thead>
            <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>说明</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>out_trade_no</td>
                <td>string</td>
                <td>是</td>
                <td>商户订单号</td>
            </tr>
            <tr>
                <td>amount</td>
                <td>float</td>
                <td>是</td>
                <td>订单金额</td>
            </tr>
            <tr>
                <td>channel_code</td>
                <td>string</td>
                <td>是</td>
                <td>支付渠道代码</td>
            </tr>
            <tr>
                <td>status</td>
                <td>string</td>
                <td>是</td>
                <td>支付状态 status=1 成功 status=0 失败</td>
            </tr>
            <tr>
                <td>sign</td>
                <td>string</td>
                <td>是</td>
                <td>签名，详见签名规则</td>
            </tr>
        </tbody>
    </table>

    <h3>签名规则</h3>
    <ol>
        <li>将除sign参数外的所有参数按照参数名ASCII码从小到大排序</li>
        <li>使用key-value格式拼接参数，并以&符号连接</li>
        <li>在最后加上&key=商户密钥</li>
        <li>对拼接后的字符串进行MD5加密，得到签名</li>
    </ol>
    
    <h3>响应要求</h3>
    <ol>
        <li>成功：返回 "SUCCESS" 或 "success"</li>
        <li>失败：返回其他字符串</li>
    </ol>
</body>
</html>