(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-384e44a7"],{"0ccb":function(t,e,a){var r=a("50c4"),n=a("1148"),o=a("1d80"),i=Math.ceil,l=function(t){return function(e,a,l){var s,c,u=String(o(e)),d=u.length,f=void 0===l?" ":String(l),m=r(a);return m<=d||""==f?u:(s=m-d,c=n.call(f,i(s/f.length)),c.length>s&&(c=c.slice(0,s)),t?u+c:c+u)}};t.exports={start:l(!1),end:l(!0)}},1148:function(t,e,a){"use strict";var r=a("a691"),n=a("1d80");t.exports="".repeat||function(t){var e=String(n(this)),a="",o=r(t);if(o<0||o==1/0)throw RangeError("Wrong number of repetitions");for(;o>0;(o>>>=1)&&(e+=e))1&o&&(a+=e);return a}},"3e1f":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"收款账号"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.name,callback:function(e){t.$set(t.listQuery,"name",e)},expression:"listQuery.name"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"备注"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.remark,callback:function(e){t.$set(t.listQuery,"remark",e)},expression:"listQuery.remark"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"创建人"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter(e)}},model:{value:t.listQuery.nickname,callback:function(e){t.$set(t.listQuery,"nickname",e)},expression:"listQuery.nickname"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}},[a("span",{style:{color:e.key?"#13ce66":"#ff4949"}},[t._v(t._s(e.label))])])})),1),a("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"是否异常",clearable:""},model:{value:t.listQuery.hasError,callback:function(e){t.$set(t.listQuery,"hasError",e)},expression:"listQuery.hasError"}},t._l(t.errorOptions,(function(t){return a("el-option",{key:t.key,attrs:{label:t.label,value:t.key}})})),1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary"},on:{click:t.handleFilter}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"success"},on:{click:t.handleAdd}},[t._v(" 新增 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"compact-table",staticStyle:{width:"100%"},attrs:{data:t.tableData,border:"",stripe:"","highlight-current-row":"",size:"mini"}},[a("el-table-column",{attrs:{type:"selection",width:"40",align:"center"}}),a("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"60"}}),a("el-table-column",{attrs:{prop:"name",label:"收款账号","min-width":"100",align:"center"}}),a("el-table-column",{attrs:{prop:"diamond",label:"号内余额",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"diamond-amount"},[t._v(t._s(e.row.diamond||0))])]}}])}),a("el-table-column",{attrs:{label:"收款统计",align:"center"}},[a("el-table-column",{attrs:{prop:"todayAmount",label:"今日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"today-amount"},[t._v(t._s(t.formatAmount(e.row.todayAmount)))]),a("br"),a("span",{staticClass:"today-count"},[t._v(t._s(e.row.todayCount)+"笔")])]}}])}),a("el-table-column",{attrs:{prop:"yesterdayAmount",label:"昨日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-text"},[t._v(t._s(t.formatAmount(e.row.yesterdayAmount)))]),a("br"),a("span",{staticClass:"amount-text"},[t._v(t._s(e.row.yesterdayCount)+"笔")])]}}])}),a("el-table-column",{attrs:{prop:"beforeAmount",label:"前日收款",width:"90",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"amount-text"},[t._v(t._s(t.formatAmount(e.row.beforeAmount)))]),a("br"),a("span",{staticClass:"amount-text"},[t._v(t._s(e.row.beforeCount)+"笔")])]}}])})],1),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"70",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{prop:"errorMsg",label:"异常信息","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{class:{"error-msg":e.row.errormsg}},[t._v(t._s(e.row.errormsg||"-"))])]}}])}),a("el-table-column",{attrs:{prop:"remark",label:"备注","min-width":"100",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"nickname",label:"创建人","min-width":"90",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatTime(e.row.createtime))+" ")]}}])}),a("el-table-column",{attrs:{prop:"requestTime",label:"请求时间","min-width":"140",align:"center","class-name":"time-column"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatTime(e.row.pulltime))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v(" 编辑 ")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])})],1),a("div",{staticClass:"pagination-container"},[a("el-pagination",{staticClass:"compact-pagination",attrs:{background:"","current-page":t.currentPage,"page-sizes":[10,20,30,50],"page-size":t.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:t.total,small:""},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),a("el-dialog",{attrs:{title:t.dialogTitle,visible:t.dialogVisible,width:"500px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e},close:t.handleDialogClose}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px",size:"small"}},[a("el-form-item",{attrs:{label:"登录二维码"}},[a("div",{staticClass:"qr-code-container"},[t.qrCodeUrl?a("img",{staticClass:"qr-code",attrs:{src:t.qrCodeUrl,alt:"登录二维码"}}):t._e(),t.qrCodeUrl?a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.getQrCode}},[t._v("刷新二维码")]):a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.getQrCode}},[t._v("获取二维码")])],1)]),a("el-form-item",{attrs:{label:"收款账号",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入收款账号"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"Cookie",prop:"cookie"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入cookie"},model:{value:t.form.cookie,callback:function(e){t.$set(t.form,"cookie",e)},expression:"form.cookie"}})],1),a("el-form-item",{attrs:{label:"状态"}},[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}})],1),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入备注信息"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")])],1)],1)],1)},n=[],o=a("5530"),i=(a("99af"),a("7db0"),a("d81d"),a("b0c0"),a("b680"),a("d3b7"),a("25f0"),a("4d90"),a("0643"),a("fffc"),a("a573"),a("b775"));function l(t){return Object(i["a"])({url:"/account/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/account/add",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/account/delete",method:"post",data:{id:t}})}function u(t){return Object(i["a"])({url:"/account/update",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/account/status",method:"post",data:t})}function f(t){return Object(i["a"])({url:"/account/detail",method:"get",params:{id:t}})}var m={name:"Account",data:function(){return{listQuery:{name:"",remark:"",nickname:"",status:"",hasError:"",page:1,limit:10},statusOptions:[{key:1,label:"开启"},{key:0,label:"禁用"}],errorOptions:[{key:1,label:"是"},{key:0,label:"否"}],tableData:[],currentPage:1,pageSize:10,total:0,loading:!1,dialogVisible:!1,dialogTitle:"",form:{name:"",status:1,remark:"",cookie:""},rules:{name:[{required:!0,message:"请输入收款账号",trigger:"blur"}],cookie:[{required:!0,message:"请输入cookie",trigger:"blur"}]},qrCodeUrl:"",qrId:"",qrTimer:null}},created:function(){this.getList()},methods:{formatTime:function(t){if(!t)return"-";var e=new Date(t),a=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),i=e.getMinutes().toString().padStart(2,"0"),l=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(n," ").concat(o,":").concat(i,":").concat(l)},formatAmount:function(t){if(void 0===t||null===t)return"¥0.00";var e=parseFloat(t);return isNaN(e)?"¥0.00":"¥".concat(e.toFixed(2))},getList:function(){var t=this;this.loading=!0;var e=Object(o["a"])({},this.listQuery);""!==e.hasError&&void 0!==e.hasError&&(e.hasErrorMsg=1===e.hasError),delete e.hasError,l(e).then((function(e){var a=e.data,r=a.list,n=a.total,i=a.page,l=a.limit;t.tableData=r.map((function(t){var e=Object(o["a"])(Object(o["a"])({},t),{},{status:1===t.status,todayAmount:t.today_amount,yesterdayAmount:t.yesterday_amount,beforeAmount:t.before_amount,beforeCount:t.before_count,todayCount:t.today_count,yesterdayCount:t.yesterday_count,diamond:t.diamond||0});return e})),t.total=n,t.currentPage=i,t.pageSize=l,t.loading=!1}))},mapPayType:function(t){return t},mapPayTypeToBackend:function(t){return t},handleFilter:function(){this.listQuery.page=1,this.getList()},handleAdd:function(){this.dialogTitle="新增账号",this.dialogVisible=!0,this.resetTemp()},handleEdit:function(t){var e=this;this.dialogTitle="编辑账号",f(t.id).then((function(t){var a,r=t.data;e.form=Object(o["a"])(Object(o["a"])({},r),{},{status:1===r.status?1:0,cookie:(null===(a=r.config)||void 0===a?void 0:a.cookie)||""}),e.dialogVisible=!0}))},resetTemp:function(){this.form={name:"",status:1,remark:"",cookie:""}},handleDelete:function(t){var e=this;this.$confirm("确认删除该账号吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c(t.id).then((function(t){e.$message.success("删除成功"),e.getList()}))})).catch((function(){e.$message.info("已取消删除")}))},handleSizeChange:function(t){this.pageSize=t,this.listQuery.limit=t,this.getList()},handleCurrentChange:function(t){this.currentPage=t,this.listQuery.page=t,this.getList()},handleFormSubmit:function(t){var e=this,a=Object(o["a"])({},t);delete a.payType,"新增账号"===this.dialogTitle?s(a).then((function(t){e.$message.success("新增成功"),e.dialogVisible=!1,e.getList()})):u(a).then((function(t){e.$message.success("编辑成功"),e.dialogVisible=!1,e.getList()}))},handleStatusChange:function(t){var e=this,a=t.status?1:0;d({id:t.id,status:a}).then((function(t){e.$message.success("状态已".concat(1===a?"开启":"禁用"))}))},getPayTypeTagType:function(t){var e=this.payTypeOptions.find((function(e){return e.key===t}));return e?e.tagType:"info"},getPayTypeLabel:function(t){var e=this.payTypeOptions.find((function(e){return e.key===t})),a=e?e.label:t;return a},getQrCode:function(){var t=this;Object(i["a"])({url:"/account/getQeid",method:"get"}).then((function(e){1===e.code&&e.data&&e.data.qrId?(t.qrId=e.data.qrId,t.qrCodeUrl="https://udblgn.huya.com/qrLgn/getQrImg?appId=5002&k=".concat(t.qrId),t.startQrCheck()):t.$message.error(e.msg||"获取二维码失败")})).catch((function(e){console.error("获取二维码失败:",e),t.$message.error("获取二维码失败")}))},startQrCheck:function(){var t=this;this.stopQrCheck(),this.qrTimer=setInterval((function(){t.checkQrLogin()}),3e3)},stopQrCheck:function(){this.qrTimer&&(clearInterval(this.qrTimer),this.qrTimer=null)},checkQrLogin:function(){var t=this;this.qrId&&Object(i["a"])({url:"/account/tryQrcode",method:"get",params:{qrId:this.qrId}}).then((function(e){1===e.code&&e.data&&(t.form.name=e.data.name||"",t.form.cookie=e.data.cookie||"",t.stopQrCheck(),t.$message.success("登录成功"))})).catch((function(t){console.error("检查登录状态失败:",t)}))},handleDialogClose:function(){this.resetTemp(),this.qrCodeUrl="",this.qrId="",this.stopQrCheck()},submitForm:function(){var t=this;this.$refs.form.validate((function(e){e?t.handleFormSubmit(t.form):t.$message.error("表单验证失败")}))}}},p=m,h=(a("a850"),a("2877")),g=Object(h["a"])(p,r,n,!1,null,"1480ddb7",null);e["default"]=g.exports},"408a":function(t,e,a){var r=a("c6b6");t.exports=function(t){if("number"!=typeof t&&"Number"!=r(t))throw TypeError("Incorrect invocation");return+t}},"4d90":function(t,e,a){"use strict";var r=a("23e7"),n=a("0ccb").start,o=a("9a0c");r({target:"String",proto:!0,forced:o},{padStart:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}})},"7d2a":function(t,e,a){},"7db0":function(t,e,a){"use strict";var r=a("23e7"),n=a("b727").find,o=a("44d2"),i=a("ae40"),l="find",s=!0,c=i(l);l in[]&&Array(1)[l]((function(){s=!1})),r({target:"Array",proto:!0,forced:s||!c},{find:function(t){return n(this,t,arguments.length>1?arguments[1]:void 0)}}),o(l)},"9a0c":function(t,e,a){var r=a("342f");t.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(r)},a850:function(t,e,a){"use strict";a("7d2a")},b680:function(t,e,a){"use strict";var r=a("23e7"),n=a("a691"),o=a("408a"),i=a("1148"),l=a("d039"),s=1..toFixed,c=Math.floor,u=function(t,e,a){return 0===e?a:e%2===1?u(t,e-1,a*t):u(t*t,e/2,a)},d=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},f=s&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0))||!l((function(){s.call({})}));r({target:"Number",proto:!0,forced:f},{toFixed:function(t){var e,a,r,l,s=o(this),f=n(t),m=[0,0,0,0,0,0],p="",h="0",g=function(t,e){var a=-1,r=e;while(++a<6)r+=t*m[a],m[a]=r%1e7,r=c(r/1e7)},b=function(t){var e=6,a=0;while(--e>=0)a+=m[e],m[e]=c(a/t),a=a%t*1e7},y=function(){var t=6,e="";while(--t>=0)if(""!==e||0===t||0!==m[t]){var a=String(m[t]);e=""===e?a:e+i.call("0",7-a.length)+a}return e};if(f<0||f>20)throw RangeError("Incorrect fraction digits");if(s!=s)return"NaN";if(s<=-1e21||s>=1e21)return String(s);if(s<0&&(p="-",s=-s),s>1e-21)if(e=d(s*u(2,69,1))-69,a=e<0?s*u(2,-e,1):s/u(2,e,1),a*=4503599627370496,e=52-e,e>0){g(0,a),r=f;while(r>=7)g(1e7,0),r-=7;g(u(10,r,1),0),r=e-1;while(r>=23)b(1<<23),r-=23;b(1<<r),g(1,1),b(2),h=y()}else g(0,a),g(1<<-e,0),h=y()+i.call("0",f);return f>0?(l=h.length,h=p+(l<=f?"0."+i.call("0",f-l)+h:h.slice(0,l-f)+"."+h.slice(l-f))):h=p+h,h}})},fffc:function(t,e,a){"use strict";var r=a("23e7"),n=a("2266"),o=a("1c0b"),i=a("825a");r({target:"Iterator",proto:!0,real:!0},{find:function(t){return i(this),o(t),n(this,(function(e){if(t(e))return n.stop(e)}),void 0,!1,!0).result}})}}]);