<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 账号接口
 */
class Account extends Api
{

    protected $noNeedLogin = [];
    protected $noNeedRight = ['tryQrcode', 'getQeid', 'export'];

    /**
     * 获取账号列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'paytype'   => input('payType/s', ''),
            'status'    => input('status/s', ''),
            'remark'    => input('remark/s', ''),
            'nickname'   => input('nickname/s', ''),
            'hasErrorMsg'   => input('hasErrorMsg/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的账号
            $where['user_id'] = $this->auth->id;
        }
        // 构建查询条件
        if ($params['name'] !== '') {
            $where['a.name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['paytype'] !== '') {
            $where['a.paytype'] = $params['paytype'];
        }
        if ($params['status'] !== '') {
            $where['a.status'] = $params['status'];
        }
        if ($params['remark'] !== '') {
            $where['a.remark'] = ['like', "%{$params['remark']}%"];
        }
        if ($params['nickname'] !== '') {
            $where['u.nickname'] = ['like', "%{$params['nickname']}%"];
        }
        if ($params['hasErrorMsg'] == 'true') {
            // errormsg不为空的账号
            $where['a.errormsg'] = ['neq', ''];
        }
        if ($params['hasErrorMsg'] == 'false') {
            // errormsg为空的账号
            $where['a.errormsg'] = ['eq', ''];
        }
        // 查询数据
        $list = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->field('a.diamond,a.id,a.name,a.paytype,a.status,a.errormsg,a.remark,from_unixtime(a.createtime,"%Y-%m-%d %H:%i:%s") as createtime,from_unixtime(a.pulltime,"%Y-%m-%d %H:%i:%s") as pulltime,u.nickname')
            ->where($where)
            ->order('a.diamond', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
        // 获取前日开始和结束时间戳
        $beforeStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 将Collection对象转换为数组
        $listArray = collection($list)->toArray();

        // 获取所有账号ID
        $accountIds = array_column($listArray, 'id');

        if (!empty($accountIds)) {
            // 使用单次查询获取所有统计数据
            $statsSql = \think\Db::name('order')
                ->field([
                    'account_id',
                    // 统计今日订单数量（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $todayStart . ' AND ' . $todayEnd . ' THEN 1 ELSE 0 END) as today_count',
                    // 统计昨日订单数量（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $yesterdayStart . ' AND ' . $yesterdayEnd . ' THEN 1 ELSE 0 END) as yesterday_count',
                    // 统计前日订单数量（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $beforeStart . ' AND ' . $beforeEnd . ' THEN 1 ELSE 0 END) as before_count',

                    // 统计今日订单金额（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $todayStart . ' AND ' . $todayEnd . ' THEN amount ELSE 0 END) as today_amount',
                    // 统计昨日订单金额（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $yesterdayStart . ' AND ' . $yesterdayEnd . ' THEN amount ELSE 0 END) as yesterday_amount',
                    // 统计前日订单金额（account_id不为空的）
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $beforeStart . ' AND ' . $beforeEnd . ' THEN amount ELSE 0 END) as before_amount'
                ])
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->buildSql();

            $statsResults = \think\Db::table($statsSql . ' stats')
                ->select();

            // 将查询结果转换为以account_id为键的关联数组
            $statsMap = [];
            foreach ($statsResults as $item) {
                $statsMap[$item['account_id']] = [
                    'today_count' => $item['today_count'],
                    'before_count' => $item['before_count'],
                    'yesterday_count' => $item['yesterday_count'],

                    'today_amount' => $item['today_amount'],
                    'yesterday_amount' => $item['yesterday_amount'],
                    'before_amount' => $item['before_amount']
                ];
            }

            // 为每个账号添加收款统计
            foreach ($list as &$item) {
                if (isset($statsMap[$item['id']])) {
                    $item['today_count'] = $statsMap[$item['id']]['today_count'];
                    $item['yesterday_count'] = $statsMap[$item['id']]['yesterday_count'];
                    $item['before_count'] = $statsMap[$item['id']]['before_count'];

                    $item['today_amount'] = number_format($statsMap[$item['id']]['today_amount'], 2, '.', '') ?: '0.00';
                    $item['yesterday_amount'] = number_format($statsMap[$item['id']]['yesterday_amount'], 2, '.', '') ?: '0.00';
                    $item['before_amount'] = number_format($statsMap[$item['id']]['before_amount'], 2, '.', '') ?: '0.00';
                } else {
                    $item['today_count'] = 0;
                    $item['yesterday_count'] = 0;
                    $item['before_count'] = 0;
                    $item['today_amount'] = '0.00';
                    $item['yesterday_amount'] = '0.00';
                    $item['before_amount'] = '0.00';
                }
            }
        } else {
            // 如果没有账号，设置默认值
            foreach ($list as &$item) {
                $item['today_count'] = 0;
                $item['yesterday_count'] = 0;
                $item['before_count'] = 0;
                $item['today_amount'] = '0.00';
                $item['yesterday_amount'] = '0.00';
                $item['before_amount'] = '0.00';
            }
        }

        // 查询总数
        $total = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 导出账号列表
     */
    public function export()
    {
        // 获取查询参数（与list接口完全一致）
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'paytype'   => input('payType/s', ''),
            'status'    => input('status/s', ''),
            'remark'    => input('remark/s', ''),
            'nickname'   => input('nickname/s', ''),
            'hasErrorMsg'   => input('hasErrorMsg/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能导出自己的账号
            $where['user_id'] = $this->auth->id;
        }

        // 构建查询条件（与list接口完全一致）
        if ($params['name'] !== '') {
            $where['a.name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['paytype'] !== '') {
            $where['a.paytype'] = $params['paytype'];
        }
        if ($params['status'] !== '') {
            $where['a.status'] = $params['status'];
        }
        if ($params['remark'] !== '') {
            $where['a.remark'] = ['like', "%{$params['remark']}%"];
        }
        if ($params['nickname'] !== '') {
            $where['u.nickname'] = ['like', "%{$params['nickname']}%"];
        }
        if ($params['hasErrorMsg'] == 'true') {
            // errormsg不为空的账号
            $where['a.errormsg'] = ['neq', ''];
        }
        if ($params['hasErrorMsg'] == 'false') {
            // errormsg为空的账号
            $where['a.errormsg'] = ['eq', ''];
        }

        // 查询所有数据（不分页，但使用相同的查询逻辑）
        $list = \think\Db::name('account')
            ->alias('a')
            ->join('user u', 'a.user_id = u.id', 'LEFT')
            ->field('a.diamond,a.id,a.name,a.paytype,a.status,a.errormsg,a.remark,from_unixtime(a.createtime,"%Y-%m-%d %H:%i:%s") as createtime,from_unixtime(a.pulltime,"%Y-%m-%d %H:%i:%s") as pulltime,u.nickname')
            ->where($where)
            ->order('a.diamond', 'desc')
            ->select();

        // 检查是否有数据
        if (empty($list)) {
            $this->error('没有找到符合条件的账号数据');
        }

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取前日开始和结束时间戳
        $beforeStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 将Collection对象转换为数组
        $listArray = collection($list)->toArray();

        // 获取所有账号ID
        $accountIds = array_column($listArray, 'id');

        if (!empty($accountIds)) {
            // 使用单次查询获取所有统计数据
            $statsSql = \think\Db::name('order')
                ->field([
                    'account_id',
                    // 统计今日订单数量
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $todayStart . ' AND ' . $todayEnd . ' THEN 1 ELSE 0 END) as today_count',
                    // 统计昨日订单数量
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $yesterdayStart . ' AND ' . $yesterdayEnd . ' THEN 1 ELSE 0 END) as yesterday_count',
                    // 统计前日订单数量
                    'SUM(CASE WHEN pay_status = 1 AND account_id IS NOT NULL AND callback_time BETWEEN ' . $beforeStart . ' AND ' . $beforeEnd . ' THEN 1 ELSE 0 END) as before_count',

                    // 统计今日订单金额
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $todayStart . ' AND ' . $todayEnd . ' THEN amount ELSE 0 END) as today_amount',
                    // 统计昨日订单金额
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $yesterdayStart . ' AND ' . $yesterdayEnd . ' THEN amount ELSE 0 END) as yesterday_amount',
                    // 统计前日订单金额
                    'SUM(CASE WHEN pay_status = 1 AND callback_time BETWEEN ' . $beforeStart . ' AND ' . $beforeEnd . ' THEN amount ELSE 0 END) as before_amount'
                ])
                ->where('account_id', 'in', $accountIds)
                ->group('account_id')
                ->buildSql();

            $statsResults = \think\Db::table($statsSql . ' stats')
                ->select();

            // 将查询结果转换为以account_id为键的关联数组
            $statsMap = [];
            foreach ($statsResults as $item) {
                $statsMap[$item['account_id']] = [
                    'today_count' => $item['today_count'],
                    'before_count' => $item['before_count'],
                    'yesterday_count' => $item['yesterday_count'],
                    'today_amount' => $item['today_amount'],
                    'yesterday_amount' => $item['yesterday_amount'],
                    'before_amount' => $item['before_amount']
                ];
            }

            // 为每个账号添加收款统计
            foreach ($list as &$item) {
                if (isset($statsMap[$item['id']])) {
                    $item['today_count'] = $statsMap[$item['id']]['today_count'];
                    $item['yesterday_count'] = $statsMap[$item['id']]['yesterday_count'];
                    $item['before_count'] = $statsMap[$item['id']]['before_count'];
                    $item['today_amount'] = number_format($statsMap[$item['id']]['today_amount'], 2, '.', '') ?: '0.00';
                    $item['yesterday_amount'] = number_format($statsMap[$item['id']]['yesterday_amount'], 2, '.', '') ?: '0.00';
                    $item['before_amount'] = number_format($statsMap[$item['id']]['before_amount'], 2, '.', '') ?: '0.00';
                } else {
                    $item['today_count'] = 0;
                    $item['yesterday_count'] = 0;
                    $item['before_count'] = 0;
                    $item['today_amount'] = '0.00';
                    $item['yesterday_amount'] = '0.00';
                    $item['before_amount'] = '0.00';
                }
            }
        } else {
            // 如果没有账号，设置默认值
            foreach ($list as &$item) {
                $item['today_count'] = 0;
                $item['yesterday_count'] = 0;
                $item['before_count'] = 0;
                $item['today_amount'] = '0.00';
                $item['yesterday_amount'] = '0.00';
                $item['before_amount'] = '0.00';
            }
        }

        // 准备导出数据
        $exportData = [];

        // 添加表头
        $exportData[] = [
            'ID',
            '账号名称',
            '支付类型',
            '状态',
            '号内余额',
            '错误信息',
            '备注',
            '创建时间',
            '拉取时间',
            '用户昵称',
            '今日订单数',
            '昨日订单数',
            '前日订单数',
            '今日金额',
            '昨日金额',
            '前日金额'
        ];

        // 添加数据行
        foreach ($list as $item) {
            $statusText = $item['status'] == 1 ? '正常' : '禁用';
            $paytypeText = $item['paytype'] == 'huya' ? '虎牙' : $item['paytype'];

            $exportData[] = [
                $item['id'],
                $item['name'],
                $paytypeText,
                $statusText,
                $item['diamond'],
                $item['errormsg'] ?: '',
                $item['remark'] ?: '',
                $item['createtime'],
                $item['pulltime'],
                $item['nickname'] ?: '',
                $item['today_count'],
                $item['yesterday_count'],
                $item['before_count'],
                $item['today_amount'],
                $item['yesterday_amount'],
                $item['before_amount']
            ];
        }

        // 生成CSV文件
        $filename = '账号列表_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'export' . DS;

        // 确保目录存在
        if (!is_dir($filepath)) {
            if (!mkdir($filepath, 0755, true)) {
                throw new \Exception('创建导出目录失败');
            }
        }

        $fullpath = $filepath . $filename;

        // 写入CSV文件
        $fp = fopen($fullpath, 'w');
        if ($fp === false) {
            throw new \Exception('无法创建导出文件');
        }

        // 添加BOM头，解决中文乱码问题
        fwrite($fp, "\xEF\xBB\xBF");

        foreach ($exportData as $row) {
            if (fputcsv($fp, $row) === false) {
                fclose($fp);
                throw new \Exception('写入CSV数据失败');
            }
        }
        fclose($fp);

        // 检查文件是否成功创建
        if (!file_exists($fullpath)) {
            throw new \Exception('导出文件创建失败');
        }

        // 清理7天前的导出文件
        $this->cleanOldExportFiles($filepath);

        // 返回下载链接
        $downloadUrl = $this->request->domain() . '/uploads/export/' . $filename;

        $this->success('导出成功', [
            'download_url' => $downloadUrl,
            'filename' => $filename,
            'total_count' => count($list)
        ]);
    }

    /**
     * 清理过期的导出文件
     * @param string $filepath 文件路径
     */
    private function cleanOldExportFiles($filepath)
    {
        try {
            $files = glob($filepath . '*.csv');
            $expireTime = time() - 7 * 24 * 60 * 60; // 7天前

            foreach ($files as $file) {
                if (filemtime($file) < $expireTime) {
                    unlink($file);
                }
            }
        } catch (\Exception $e) {
            // 清理失败不影响主流程，只记录日志
            \think\Log::warning('清理过期导出文件失败：' . $e->getMessage());
        }
    }

    /**
     * 添加账号
     */
    public function add()
    {
        $params = input();
        $params['config'] = '';
        if ($params['cookie']) {
            $config['cookie'] = $params['cookie'];
            $params['config'] = json_encode($config);
        }
        Db::startTrans();
        try {
            $account = new \app\common\model\Account();
            if ($account->where('name', $params['name'])->find()) {
                throw new \Exception('账号已存在');
            }
            $data['pulltime'] = time();
            $data['name'] = $params['name'];
            $data['paytype'] = 'huya';
            $data['status'] = 1;
            $data['remark'] = $params['remark'];
            $data['user_id'] = $this->auth->id;
            $data['config'] = $params['config'];
            $account->save($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 删除账号
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能删除自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限删除该账号');
        }
        if (!$isAdmin) {
            // 48小时内不能删除有支付订单的账号
            $order = Db::name('order')->where('account_id', $id)->where('pay_status', 1)->where('create_time', '>', time() - 48 * 60 * 60)->find();
            if ($order) {
                $this->error('48小时内不能删除有支付订单的账号');
            }
        }
        $account->delete();
        $this->success('删除账号成功');
    }

    /**
     * 修改账号状态
     */
    public function status()
    {
        $id = input('id');
        $status = input('status');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能修改自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限修改该账号状态');
        }
        $account->errormsg = '';
        $account->status = $status;
        $account->save();
        $this->success('修改账号状态成功');
    }

    /**
     * 修改账号收款状态
     */
    public function setPayStatus()
    {
        $id = input('id');
        $paystatus = input('paystatus');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        $account->paystatus = $paystatus;
        $account->save();
        $this->success('修改账号收款状态成功');
    }

    /**
     * 修改账号信息
     */
    public function update()
    {
        $params = input();
        $params['config'] = '';
        if ($params['cookie']) {
            $config['cookie'] = $params['cookie'];
            $params['config'] = json_encode($config);
        }

        Db::startTrans();
        try {
            $account = new \app\common\model\Account();
            $account = $account->where('id', $params['id'])->find();
            if (!$account) {
                throw new \Exception('账号不存在');
            }
            // 权限检查：非管理员只能修改自己的账号
            $isAdmin = $this->auth->group_id === 1;
            $isOwner = $account['user_id'] === $this->auth->id;
            if (!$isAdmin && !$isOwner) {
                throw new \Exception('您没有权限修改该账号');
            }
            $data['errormsg'] = '';
            $data['name'] = $params['name'];
            $data['paytype'] = 'huya';
            $data['status'] = $params['status'];
            $data['remark'] = $params['remark'];
            $data['config'] = $params['config'];
            $account->save($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('修改账号信息成功');
    }

    /**
     * 获取账号信息
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('账号ID不能为空');
        }
        $account = \app\common\model\Account::where('id', $id)->find();
        if (!$account) {
            $this->error('账号不存在');
        }
        // 权限检查：非管理员只能查看自己的账号
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $account['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限查看该账号');
        }
        $account->config = json_decode($account->config, true);
        $this->success('获取账号信息成功', $account);
    }

    public function getQeid()
    {
        $qrId = \huya\Service::getQeid();
        $this->success('获取成功', ['qrId' => $qrId]);
    }

    public function tryQrcode()
    {
        $params = input();
        $res = \huya\Service::tryQrLogin($params['qrId']);
        if ($res['data']['stage'] !== 2) {
            $this->error("等待扫码中");
        }
        $cookieList = $res['cookie']['headers']['Set-Cookie'];
        $cookie = "";
        $hyname = "";
        foreach ($cookieList as $item) {
            $str = explode(";", $item)[0];
            $cookie = $str . ";" . $cookie;
            if (strpos($str, "username") !== false) {
                $hyname = explode("=", $str)[1];
            }
        }
        $this->success('获取账号信息成功', ['cookie' => $cookie, 'name' => $hyname]);
    }
}
