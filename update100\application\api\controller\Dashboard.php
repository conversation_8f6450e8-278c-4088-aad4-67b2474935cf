<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;

/**
 * 仪表盘接口
 */
class Dashboard extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 获取基础查询条件
     */
    protected function getBaseWhere()
    {
        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的数据
            $where['user_id'] = $this->auth->id;
        }
        return $where;
    }

    /**
     * 获取时间范围查询条件（用于未支付订单）
     */
    protected function getTimeWhere($startDate = null, $endDate = null)
    {
        $where = [];

        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $where['create_time'] = ['egt', $startTime];
        }

        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            if (isset($where['create_time'])) {
                $where['create_time'] = ['between', [$startTime, $endTime]];
            } else {
                $where['create_time'] = ['elt', $endTime];
            }
        }

        return $where;
    }

    /**
     * 获取时间范围查询条件（用于已支付订单）
     */
    protected function getTimeWhereForPaid($startDate = null, $endDate = null)
    {
        $where = [];

        if ($startDate) {
            $startTime = strtotime($startDate . ' 00:00:00');
            $where['callback_time'] = ['egt', $startTime];
        }

        if ($endDate) {
            $endTime = strtotime($endDate . ' 23:59:59');
            if (isset($where['callback_time'])) {
                $where['callback_time'] = ['between', [$startTime, $endTime]];
            } else {
                $where['callback_time'] = ['elt', $endTime];
            }
        }

        return $where;
    }

    /**
     * 获取历史区间数据（不含今天）
     */
    private function getOverviewData($baseWhere, $paytype, $startDate, $endDate)
    {
        $timeWhere = $this->getTimeWhere($startDate, $endDate);
        $paidTimeWhere = $this->getTimeWhereForPaid($startDate, $endDate);
        $totalStats = \think\Db::name('order')
            ->where($baseWhere)
            ->where($timeWhere)
            ->where('paytype', $paytype)
            ->field([
                'COUNT(*) as total_count',
                'SUM(amount) as total_amount'
            ])->find();
        $paidStats = \think\Db::name('order')
            ->where($baseWhere)
            ->where($paidTimeWhere)
            ->where('paytype', $paytype)
            ->where('pay_status', 1)
            ->field([
                'COUNT(*) as success_count',
                'SUM(amount) as success_amount'
            ])->find();
        $successRate = $totalStats['total_count'] > 0
            ? round(($paidStats['success_count'] / $totalStats['total_count']) * 100, 2)
            : 0;
        return [
            'amount' => $paidStats['success_amount'] ?: 0,
            'orderCount' => $totalStats['total_count'] ?: 0,
            'successRate' => $successRate,
            'orderTotalAmount' => $totalStats['total_amount'] ?: 0
        ];
    }

    /**
     * 获取仪表盘数据（历史自动缓存+今日实时查询+合并返回）
     */
    public function overview()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $paytype = 'huya';

            // 历史区间（不含今天）
            $historyEnd = date('Y-m-d', strtotime('-1 day'));
            $historyData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $cacheKey = 'overview_history_' . $paytype . '_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
                $historyData = \think\Cache::get($cacheKey);
                if ($historyData === false) {
                    $historyData = $this->getOverviewData($baseWhere, $paytype, $startDate, $historyEnd);
                    \think\Cache::set($cacheKey, $historyData, 86400);
                }
            }

            // 今天的数据
            $todayData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($endDate >= $today) {
                $todayData = $this->getOverviewData($baseWhere, $paytype, $today, $endDate);
            }

            // 合并
            $totalOrderCount = $historyData['orderCount'] + $todayData['orderCount'];
            $totalAmount = $historyData['amount'] + $todayData['amount'];
            $totalOrderTotalAmount = $historyData['orderTotalAmount'] + $todayData['orderTotalAmount'];
            $successRate = $totalOrderCount > 0 ? round(($totalAmount / $totalOrderTotalAmount) * 100, 2) : 0;
            $data = [
                'todayAmount' => $todayData['amount'],
                'todayOrderCount' => $todayData['orderCount'],
                'todaySuccessRate' => $todayData['successRate'],
                'todayOrderTotalAmount' => $todayData['orderTotalAmount'],
                'historyAmount' => $historyData['amount'],
                'historyOrderCount' => $historyData['orderCount'],
                'historySuccessRate' => $historyData['successRate'],
                'historyOrderTotalAmount' => $historyData['orderTotalAmount'],
                'totalAmount' => $totalAmount,
                'totalOrderCount' => $totalOrderCount,
                'totalOrderTotalAmount' => $totalOrderTotalAmount,
                'totalSuccessRate' => $successRate
            ];
            $this->success('获取仪表盘数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取支付宝数据（历史自动缓存+今日实时查询+合并返回）
     */
    public function overview_ali()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $paytype = 'yahu';

            // 历史区间（不含今天）
            $historyEnd = date('Y-m-d', strtotime('-1 day'));
            $historyData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $cacheKey = 'overview_history_' . $paytype . '_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
                $historyData = \think\Cache::get($cacheKey);
                if ($historyData === false) {
                    $historyData = $this->getOverviewData($baseWhere, $paytype, $startDate, $historyEnd);
                    \think\Cache::set($cacheKey, $historyData, 86400);
                }
            }

            // 今天的数据
            $todayData = ['amount' => 0, 'orderCount' => 0, 'successRate' => 0, 'orderTotalAmount' => 0];
            if ($endDate >= $today) {
                $todayData = $this->getOverviewData($baseWhere, $paytype, $today, $endDate);
            }

            // 合并
            $totalOrderCount = $historyData['orderCount'] + $todayData['orderCount'];
            $totalAmount = $historyData['amount'] + $todayData['amount'];
            $totalOrderTotalAmount = $historyData['orderTotalAmount'] + $todayData['orderTotalAmount'];
            $successRate = $totalOrderCount > 0 ? round(($totalAmount / $totalOrderTotalAmount) * 100, 2) : 0;
            $data = [
                'todayAmount' => $todayData['amount'],
                'todayOrderCount' => $todayData['orderCount'],
                'todaySuccessRate' => $todayData['successRate'],
                'todayOrderTotalAmount' => $todayData['orderTotalAmount'],
                'historyAmount' => $historyData['amount'],
                'historyOrderCount' => $historyData['orderCount'],
                'historySuccessRate' => $historyData['successRate'],
                'historyOrderTotalAmount' => $historyData['orderTotalAmount'],
                'totalAmount' => $totalAmount,
                'totalOrderCount' => $totalOrderCount,
                'totalOrderTotalAmount' => $totalOrderTotalAmount,
                'totalSuccessRate' => $successRate
            ];
            $this->success('获取仪表盘数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取历史区间渠道数据（不含今天）
     */
    private function getChannelsNewData($baseWhere, $paytype, $startDate, $endDate)
    {
        $timeWhere = $this->getTimeWhere($startDate, $endDate);
        $stats = \think\Db::name('order')
            ->where($baseWhere)
            ->where($timeWhere)
            ->where('paytype', $paytype)
            ->field([
                'FROM_UNIXTIME(create_time, "%Y-%m-%d") as date',
                'COUNT(*) as total_count',
                'SUM(amount) as total_amount',
                'SUM(CASE WHEN pay_status = 1 AND callback_time IS NOT NULL THEN 1 ELSE 0 END) as success_count',
                'SUM(CASE WHEN pay_status = 1 AND callback_time IS NOT NULL THEN amount ELSE 0 END) as success_amount'
            ])
            ->group('date')
            ->order('date asc')
            ->select();
        $data = [];
        foreach ($stats as $stat) {
            $successRate = $stat['total_count'] > 0
                ? round(($stat['success_count'] / $stat['total_count']) * 100, 2)
                : 0;
            $data[] = [
                'date' => $stat['date'],
                'volumeAmount' => $stat['total_amount'] ?: 0,
                'amount' => $stat['success_amount'] ?: 0,
                'orderCount' => $stat['total_count'] ?: 0,
                'successOrderCount' => $stat['success_count'] ?: 0,
                'successRate' => $successRate
            ];
        }
        return $data;
    }

    /**
     * 渠道数据（只统计历史数据，不含今天）
     */
    public function channels_new()
    {
        try {
            $today = date('Y-m-d');
            $startDate = input('startDate/s', $today);
            $endDate = input('endDate/s', $today);
            $baseWhere = $this->getBaseWhere();
            $historyEnd = date('Y-m-d', strtotime('-1 day'));

            // 历史数据缓存key
            $wechatHistoryKey = 'channels_new_history_wechat_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
            $alipayHistoryKey = 'channels_new_history_alipay_' . $this->auth->id . '_' . $startDate . '_' . $historyEnd;
            $wechatHistory = [];
            $alipayHistory = [];
            if ($startDate < $today && $historyEnd >= $startDate) {
                $wechatHistory = \think\Cache::get($wechatHistoryKey);
                if ($wechatHistory === false) {
                    $wechatHistory = $this->getChannelsNewData($baseWhere, 'huya', $startDate, $historyEnd);
                    \think\Cache::set($wechatHistoryKey, $wechatHistory, 86400);
                }
                $alipayHistory = \think\Cache::get($alipayHistoryKey);
                if ($alipayHistory === false) {
                    $alipayHistory = $this->getChannelsNewData($baseWhere, 'yahu', $startDate, $historyEnd);
                    \think\Cache::set($alipayHistoryKey, $alipayHistory, 86400);
                }
            }

            // 不统计今天的数据
            $data = [
                'wechat' => $wechatHistory,
                'alipay' => $alipayHistory
            ];
            $this->success('获取按日期统计数据成功', $data);
        } catch (Exception $e) {
            $this->error('获取数据失败: ' . $e->getMessage());
        }
    }
}
