<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 商户接口
 */
class Merchant extends Api
{

    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['setRate', 'detail'];

    /**
     * 获取商户列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status'    => input('status/d', '')
        ];

        $where = [];
        // 构建查询条件
        if ($params['name'] !== '') {
            $where['name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['status'] !== '') {
            $where['status'] = $params['status'];
        }

        // 查询数据
        $list = \think\Db::name('merchant')
            ->field('id,name,key,status,remark')
            ->where($where)
            ->order('id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取前日开始和结束时间戳
        $beforeYesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeYesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 为每个商户添加收款统计
        foreach ($list as &$item) {
            // 查询今日收款 - 微信
            $todayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('paytype', 'huya')
                ->where('callback_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount');

            // 查询今日收款 - 支付宝
            $todayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('paytype', 'yahu')
                ->where('callback_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount');

            // 查询昨日收款 - 微信
            $yesterdayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('callback_status', 'huya')
                ->where('callback_time', 'between', [$yesterdayStart, $yesterdayEnd])
                ->sum('amount');

            // 查询昨日收款 - 支付宝
            $yesterdayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('callback_status', 'yahu')
                ->where('callback_time', 'between', [$yesterdayStart, $yesterdayEnd])
                ->sum('amount');

            // 查询前日收款 - 微信
            $beforeYesterdayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('callback_status', 'huya')
                ->where('callback_time', 'between', [$beforeYesterdayStart, $beforeYesterdayEnd])
                ->sum('amount');

            // 查询前日收款 - 支付宝
            $beforeYesterdayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('callback_status', 1)
                ->where('paytype', 'yahu')
                ->where('callback_time', 'between', [$beforeYesterdayStart, $beforeYesterdayEnd])
                ->sum('amount');

            // 添加到结果中
            $item['today_wechat_amount'] = $todayWechatAmount ? number_format($todayWechatAmount, 2, '.', '') : '0.00';
            $item['today_alipay_amount'] = $todayAlipayAmount ? number_format($todayAlipayAmount, 2, '.', '') : '0.00';
            $item['yesterday_wechat_amount'] = $yesterdayWechatAmount ? number_format($yesterdayWechatAmount, 2, '.', '') : '0.00';
            $item['yesterday_alipay_amount'] = $yesterdayAlipayAmount ? number_format($yesterdayAlipayAmount, 2, '.', '') : '0.00';
            $item['before_yesterday_wechat_amount'] = $beforeYesterdayWechatAmount ? number_format($beforeYesterdayWechatAmount, 2, '.', '') : '0.00';
            $item['before_yesterday_alipay_amount'] = $beforeYesterdayAlipayAmount ? number_format($beforeYesterdayAlipayAmount, 2, '.', '') : '0.00';

            // 查询当前费率（fa_rate表中该商户最新一条）
            $rateRow = \think\Db::name('rate')->where('merchant_id', $item['id'])->order('createtime', 'desc')->find();
            $item['current_rate'] = $rateRow ? $rateRow['rate'] : null;
        }

        // 查询总数
        $total = \think\Db::name('merchant')
            ->alias('m')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 添加商户
     */
    public function add()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            if ($merchant->where('key', $params['key'])->find()) {
                throw new \Exception('key已存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 删除商户
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->delete();
        $this->success('删除商户成功');
    }

    /**
     * 修改商户状态
     */
    public function status()
    {
        $id = input('id');
        $status = input('status');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->status = $status;
        $merchant->save();
        $this->success('修改商户状态成功');
    }

    /**
     * 修改商户信息
     */
    public function update()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            $merchant = $merchant->where('id', $params['id'])->find();
            if (!$merchant) {
                throw new \Exception('商户不存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('修改商户信息成功');
    }

    /**
     * 获取商户信息
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }

        // 只统计当天和昨天
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $dates = [$yesterday, $today];
        $paytypes = ['huya', 'yahu'];
        $periods = [];
        foreach ($paytypes as $paytype) {
            foreach ($dates as $date) {
                $dayStart = strtotime($date . ' 00:00:00');
                $dayEnd = strtotime($date . ' 23:59:59');
                // 查询当天所有该paytype的费率设置点
                $rateList = \think\Db::name('rate')
                    ->where('merchant_id', $id)
                    ->where('paytype', $paytype)
                    ->where('createtime', 'between', [$dayStart, $dayEnd])
                    ->order('createtime', 'asc')
                    ->select();
                $segments = [];
                $lastTime = $dayStart;
                // 分段点：先收集所有分段点（含0点、所有设置点、23:59:59）
                $splitPoints = [$dayStart];
                foreach ($rateList as $rateRow) {
                    $splitPoints[] = $rateRow['createtime'];
                }
                $splitPoints[] = $dayEnd;
                // 去重并排序
                $splitPoints = array_unique($splitPoints);
                sort($splitPoints);
                // 遍历分段
                for ($i = 0; $i < count($splitPoints) - 1; $i++) {
                    $segStart = $splitPoints[$i];
                    $segEnd = $splitPoints[$i + 1];
                    // 查找分段起始时间点前最近一次设置的费率
                    $rate = \think\Db::name('rate')
                        ->where('merchant_id', $id)
                        ->where('paytype', $paytype)
                        ->where('createtime', '<=', $segStart)
                        ->order('createtime', 'desc')
                        ->value('rate');
                    $rate = $rate !== null ? $rate : 0;
                    $amount = \think\Db::name('order')
                        ->where('merchant_id', $id)
                        ->where('callback_status', 1)
                        ->where('paytype', $paytype)
                        ->where('callback_time', 'between', [$segStart, $segEnd])
                        ->sum('amount');
                    $segments[] = [
                        'start_time' => date('Y-m-d H:i:s', $segStart),
                        'end_time' => date('Y-m-d H:i:s', $segEnd),
                        'amount' => $amount ? number_format($amount, 2, '.', '') : '0.00',
                        'rate' => $rate
                    ];
                }
                if (!isset($periods[$paytype])) {
                    $periods[$paytype] = [];
                }
                $periods[$paytype][$date] = $segments;
            }
        }
        $merchant = $merchant->toArray();
        $merchant['rate_periods'] = $periods;
        $this->success('获取商户信息成功', $merchant);
    }

    /**
     * 设置商户费率
     */
    public function setRate()
    {
        $merchant_id = input('id/d');
        $rate = input('rate/f');
        $paytype = input('paytype/s');
        $createtime = input('time/d', time());
        if (!$merchant_id) {
            $this->error('商户ID不能为空');
        }
        if ($rate === null) {
            $this->error('费率不能为空');
        }
        if ($paytype !== 'huya' && $paytype !== 'yahu') {
            $this->error('支付类型错误');
        }
        // 检查商户是否存在
        $merchant = \app\common\model\Merchant::where('id', $merchant_id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        // 只做新增，不做更新
        $res = \think\Db::name('rate')->insert([
            'merchant_id' => $merchant_id,
            'rate' => $rate,
            'paytype' => $paytype,
            'createtime' => $createtime
        ]);
        if ($res === false) {
            $this->error('设置费率失败');
        }
        $this->success('设置费率成功');
    }
}
