<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 商户接口
 */
class Merchant extends Api
{

    protected $noNeedLogin = [];
    protected $noNeedRight = [];

    /**
     * 获取商户列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'name'   => input('name/s', ''),
            'status'    => input('status/d', '')
        ];

        $where = [];
        // 构建查询条件
        if ($params['name'] !== '') {
            $where['name'] = ['like', "%{$params['name']}%"];
        }
        if ($params['status'] !== '') {
            $where['status'] = $params['status'];
        }

        // 查询数据
        $list = \think\Db::name('merchant')
            ->field('id,name,key,status,remark')
            ->where($where)
            ->order('id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 获取今日开始和结束时间戳
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        // 获取昨日开始和结束时间戳
        $yesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-1 day')));
        $yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));

        // 获取前日开始和结束时间戳
        $beforeYesterdayStart = strtotime(date('Y-m-d 00:00:00', strtotime('-2 day')));
        $beforeYesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-2 day')));

        // 为每个商户添加收款统计
        foreach ($list as &$item) {
            // 查询今日收款 - 微信
            $todayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'huya')
                ->where('callback_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount');

            // 查询今日收款 - 支付宝
            $todayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'yahu')
                ->where('callback_time', 'between', [$todayStart, $todayEnd])
                ->sum('amount');

            // 查询昨日收款 - 微信
            $yesterdayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'huya')
                ->where('callback_time', 'between', [$yesterdayStart, $yesterdayEnd])
                ->sum('amount');

            // 查询昨日收款 - 支付宝
            $yesterdayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'yahu')
                ->where('callback_time', 'between', [$yesterdayStart, $yesterdayEnd])
                ->sum('amount');

            // 查询前日收款 - 微信
            $beforeYesterdayWechatAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'huya')
                ->where('callback_time', 'between', [$beforeYesterdayStart, $beforeYesterdayEnd])
                ->sum('amount');

            // 查询前日收款 - 支付宝
            $beforeYesterdayAlipayAmount = \think\Db::name('order')
                ->where('merchant_id', $item['id'])
                ->where('pay_status', 1)
                ->where('paytype', 'yahu')
                ->where('callback_time', 'between', [$beforeYesterdayStart, $beforeYesterdayEnd])
                ->sum('amount');

            // 添加到结果中
            $item['today_wechat_amount'] = $todayWechatAmount ? number_format($todayWechatAmount, 2, '.', '') : '0.00';
            $item['today_alipay_amount'] = $todayAlipayAmount ? number_format($todayAlipayAmount, 2, '.', '') : '0.00';
            $item['yesterday_wechat_amount'] = $yesterdayWechatAmount ? number_format($yesterdayWechatAmount, 2, '.', '') : '0.00';
            $item['yesterday_alipay_amount'] = $yesterdayAlipayAmount ? number_format($yesterdayAlipayAmount, 2, '.', '') : '0.00';
            $item['before_yesterday_wechat_amount'] = $beforeYesterdayWechatAmount ? number_format($beforeYesterdayWechatAmount, 2, '.', '') : '0.00';
            $item['before_yesterday_alipay_amount'] = $beforeYesterdayAlipayAmount ? number_format($beforeYesterdayAlipayAmount, 2, '.', '') : '0.00';
        }

        // 查询总数
        $total = \think\Db::name('merchant')
            ->alias('m')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 添加商户
     */
    public function add()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            if ($merchant->where('key', $params['key'])->find()) {
                throw new \Exception('key已存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('添加成功');
    }

    /**
     * 删除商户
     */
    public function delete()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->delete();
        $this->success('删除商户成功');
    }

    /**
     * 修改商户状态
     */
    public function status()
    {
        $id = input('id');
        $status = input('status');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $merchant->status = $status;
        $merchant->save();
        $this->success('修改商户状态成功');
    }

    /**
     * 修改商户信息
     */
    public function update()
    {
        $params = input();
        Db::startTrans();
        try {
            $merchant = new \app\common\model\Merchant();
            $merchant = $merchant->where('id', $params['id'])->find();
            if (!$merchant) {
                throw new \Exception('商户不存在');
            }
            $merchant->save($params);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('修改商户信息成功');
    }

    /**
     * 获取商户信息
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('商户ID不能为空');
        }
        $merchant = \app\common\model\Merchant::where('id', $id)->find();
        if (!$merchant) {
            $this->error('商户不存在');
        }
        $this->success('获取商户信息成功', $merchant);
    }
}
