<?php

namespace app\api\controller;

use app\common\controller\Api;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 订单接口
 */
class Order extends Api
{

    protected $noNeedLogin = [];
    protected $noNeedRight = [];

    /**
     * 获取订单列表
     */
    public function list()
    {
        // 获取查询参数
        $params = [
            'page'      => input('page/d', 1),
            'limit'     => input('limit/d', 10),
            'out_trade_no'   => input('out_trade_no/s', ''),
            'pay_trade_no'   => input('pay_trade_no/s', ''),
            'pay_status'     => input('pay_status/d', ''),
            'paytype'     => input('paytype/s', ''),
            'amount_min'   => input('amount_min/d', ''),
            'amount_max'   => input('amount_max/d', ''),
            'callback_status'     => input('callback_status/d', ''),
            'start_time'     => input('start_time/s', ''),
            'end_time'       => input('end_time/s', '')
        ];

        $where = [];
        if ($this->auth->group_id != 1) {
            // 非管理员只能查看自己的订单
            $where['o.user_id'] = $this->auth->id;
        }
        // ========== 核心优化1：默认时间限制 ==========
        $defaultDays = 1; // 默认查询最近1天
        if (empty($params['start_time']) && empty($params['end_time'])) {
            $where['o.create_time'] = ['>=', strtotime("-$defaultDays days")];
        }
        // 构建查询条件
        if ($params['out_trade_no'] !== '') {
            $where['o.out_trade_no'] = ['like', "%{$params['out_trade_no']}%"];
        }
        if ($params['pay_trade_no'] !== '') {
            $where['o.pay_trade_no'] = ['like', "%{$params['pay_trade_no']}%"];
        }
        if ($params['pay_status'] !== '') {
            $where['o.pay_status'] = $params['pay_status'];
        }
        if ($params['paytype'] !== '') {
            $where['o.paytype'] = $params['paytype'];
        }
        if ($params['callback_status'] !== '') {
            $where['o.callback_status'] = $params['callback_status'];
        }
        if ($params['amount_min'] !== '') {
            $where['o.amount'] = ['>=', $params['amount_min']];
        }
        if ($params['amount_max'] !== '') {
            $where['o.amount'] = ['<=', $params['amount_max']];
        }
        if ($params['start_time'] !== '') {
            $where['o.create_time'] = ['>=', strtotime($params['start_time'])];
        }
        if ($params['end_time'] !== '') {
            if (isset($where['o.create_time'])) {
                // 如果已经设置了开始时间，则添加结束时间条件
                $where['o.create_time'] = ['between', [strtotime($params['start_time']), strtotime($params['end_time'])]];
            } else {
                // 如果没有设置开始时间，只设置结束时间条件
                $where['o.create_time'] = ['<=', strtotime($params['end_time'])];
            }
        }

        // 查询数据
        $list = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('o.id,o.out_trade_no,o.pay_trade_no,o.paytype,o.create_time,
                    o.callback_time,o.amount,o.pay_status,o.callback_status,o.remark,
                    m.name as merchant_name,a.name as account_name,u.nickname as user_nickname')
            ->where($where)
            ->order('o.id', 'desc')
            ->page($params['page'], $params['limit'])
            ->select();

        // 查询总数
        $total = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->where($where)
            ->count();
        $data = [
            'total' => $total,
            'list'  => $list,
            'page'  => $params['page'],
            'limit' => $params['limit']
        ];
        $this->success('查询成功', $data);
    }

    /**
     * 获取订单详情
     */
    public function detail()
    {
        $id = input('id');
        if (!$id) {
            $this->error('订单ID不能为空');
        }

        // 查询订单详情
        $order = \think\Db::name('order')
            ->alias('o')
            ->join('merchant m', 'o.merchant_id = m.id', 'LEFT')
            ->join('account a', 'o.account_id = a.id', 'LEFT')
            ->join('user u', 'o.user_id = u.id', 'LEFT')
            ->field('u.id as uid, o.id, o.out_trade_no, o.pay_trade_no, o.paytype, 
                    o.create_time, o.callback_time, o.amount, o.pay_status, 
                    o.callback_status, o.remark, m.name as merchant_name, 
                    a.name as account_name, u.nickname as user_nickname')
            ->where('o.id', $id)
            ->find();

        // 检查订单是否存在
        if (!$order) {
            $this->error('订单不存在');
        }

        // 权限检查：非管理员只能查看自己的订单
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $order['uid'] === $this->auth->id;

        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限查看该订单');
        }

        $this->success('查询成功', $order);
    }

    /**
     * 订单回调
     */
    public function callback()
    {
        $params = input();

        // 检查参数
        if (empty($params['id'])) {
            $this->error('订单ID不能为空');
        }

        // 使用 find 方法查询订单，确保获取正确的订单数据
        $order = \app\common\model\Order::where('id', $params['id'])->find();

        // 检查订单是否存在
        if (!$order) {
            $this->error('订单不存在');
        }

        // 不能重复回调
        if ($order->callback_status == 1) {
            $this->error('订单已回调');
        }

        // 权限检查：非管理员只能回调自己的订单
        $isAdmin = $this->auth->group_id === 1;
        $isOwner = $order['user_id'] === $this->auth->id;
        if (!$isAdmin && !$isOwner) {
            $this->error('您没有权限回调该订单');
        }
        if ($order->paytype == 'huya') {
            if (empty($order->pay_trade_no)) {
                $this->error('未出单不能回调');
            }
            $account = Db::name('account')->where('id', $order->account_id)->find();
            $accountConfig = json_decode($account['config'], true);
            $cookie = $accountConfig['cookie'];
            $res = \huya\Service::status($order->pay_trade_no, $cookie);
            if ($res->code !== 0) $this->error($res->message);
            if ($res->data->status !== "PAY_SUCCESS") $this->error("订单未支付");
            Db::name('order')->where('id', $order->id)->update(['pay_status' => 1]);
        }
        $merchant = Db::name('merchant')->where('id', $order->merchant_id)->find();
        $res = \app\common\library\Order::merchantsCallback($order->callback_url, $merchant['key'], $order['out_trade_no'], $order['amount'], $order['paytype'], 1, $order['user_id']);
        if ($res) {
            $order->callback_status = 1;
            $order->callback_time = time();
            $order->save();
            $this->success('订单回调成功');
        }
        $this->error('订单回调失败');
    }
}
