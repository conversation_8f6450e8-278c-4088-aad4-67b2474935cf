<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\library\CacheLock;
use huya;
use Proxy;
use think\Config;

class Cashier extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    // 收银台
    public function index()
    {
        // 获取商户列表
        $merchants = db('merchant')
            ->field('id, name, status')
            // ->where('status', 1)
            ->order('id desc')
            ->select();

        $this->assign('merchants', $merchants);
        return $this->view->fetch();
    }

    public function pay()
    {
        $orderno = input('orderno');
        $order = db('order')->where('out_trade_no', $orderno)->find();
        $account = db('account')->where('id', $order['account_id'])->find();
        $this->assign('order', $order);
        $this->assign('account', $account);
        return $this->view->fetch();
    }

    // 收银台创建订单
    public function createOrder()
    {
        if ($this->request->isPost()) {
            $amount = $this->request->post('amount');
            $merchantId = $this->request->post('merchant_id');
            $channelCode = $this->request->post('channel');
            // 验证金额
            if (!is_numeric($amount) || $amount <= 0) {
                $this->error('请输入正确的充值金额');
            }

            // 验证商户
            if (!$merchantId) {
                $this->error('请选择商户');
            }
            $merchant = db('merchant')->where('id', $merchantId)->find();

            $data = [
                'out_trade_no' => time(),
                'mid' => $merchant['id'],
                'amount' => $amount,
                'channel_code' => $channelCode,
                'notify_url' => 'https://www.baidu.com'
            ];
            $data['sign'] = md5(urldecode(\app\common\library\Order::ascii($data)) . "&key=" . $merchant['key']);

            $res = \fast\Http::get($this->request->domain() . '/api/gateway/index', $data);
            // halt($this->request->domain() . '/api/gateway/index?' . http_build_query($data));
            $res = json_decode($res, true);

            if ($res['code'] == 1) {
                $this->redirect($res['data']['url']);
            } else {
                if (is_array($res['msg'])) {
                    $this->error(json_encode($res['msg'], JSON_UNESCAPED_UNICODE));
                } else {
                    $this->error($res['msg']);
                }
            }
        }
    }

    // 虎牙拉单
    public function huya()
    {
        $params = input();
        $order = db('order')->where('out_trade_no', $params['out_trade_no'])->find();
        $this->assign('order', $order);
        return $this->view->fetch();
    }

    /**
     * 获取支付信息 - 使用缓存锁优化版本
     * @return array|false 返回请求结果或false
     */
    public function getPayInfo()
    {
        $params = input();
        $order = db('order')->where('out_trade_no', $params['out_trade_no'])->find();
        if (empty($order)) {
            return $this->jsonResponse(0, '订单不存在');
        }

        $this->infoLog('进入拉单主流程', $order['out_trade_no']);

        // 检查订单是否已有支付链接
        if (!empty($order['pay_url'])) {
            $this->infoLog("已有支付链接，直接返回", $order['out_trade_no']);
            return $this->jsonResponse(1, '获取支付链接成功', ['url' => $order['pay_url']]);
        }

        // 尝试获取订单锁
        if (!CacheLock::acquireOrderLock($order['out_trade_no'], 15)) {
            $this->infoLog("订单正在处理中，请稍后重试", $order['out_trade_no']);
            return $this->jsonResponse(0, '订单正在处理中，请稍后重试');
        }

        try {
            // 重新检查订单状态（防止在等待锁的过程中被其他进程处理）
            $order = db('order')->where('out_trade_no', $params['out_trade_no'])->find();
            if (!empty($order['pay_url'])) {
                $this->infoLog("已有支付链接，直接返回", $order['out_trade_no']);
                return $this->jsonResponse(1, '获取支付链接成功', ['url' => $order['pay_url']]);
            }

            // 获取可用账号
            $accountList = $this->getAccountCanUseList($order['user_id']);
            if (empty($accountList)) {
                $this->errorLog('当前用户没有可用的收款账号', $order['out_trade_no']);
                return $this->jsonResponse(0, '当前用户没有可用的收款账号');
            }

            $account = $accountList[0];
            // 更新账号状态和拉单时间
            $this->updateAccountStatus($account['id']);

            // 获取代理信息
            // $clientIp = $this->request->ip();
            $clientIp = $params['ip'];
            if ($clientIp == '127.0.0.1') {
                $regionCode = '';
            } else {
                $regionCode = Proxy::getRegionCode($clientIp);
            }

            $proxy = Proxy::getProxyByShenLong(1, $regionCode);
            if (!$proxy) {
                $proxy = Proxy::getProxyByShenLong(1, "");
            }
            if (!$proxy) {
                $this->errorLog('当前IP没有可用的代理', $order['out_trade_no']);
                return $this->jsonResponse(0, '当前IP没有可用的代理');
            }

            $this->infoLog($account['id'] . '开始尝试拉单', $order['out_trade_no']);
            // 获取账号配置
            $accountConfig = json_decode($account['config'], true);

            // 获取cookie信息
            $cookie = $accountConfig['cookie'];

            // 开始拉单
            $res = huya\Pay::createOrder($order['amount'], $cookie, $order['paytype'], $proxy);

            if (!$res) {
                $this->errorLog($account['id'] . '拉单失败，错误信息：代理请求失败', $order['out_trade_no']);
                return $this->jsonResponse(0, '代理请求失败');
            }
            if (!$res['success']) {
                $this->errorLog($account['id'] . '拉单失败，错误信息：' . $res['msg'], $order['out_trade_no']);
                $this->updateAccountError($account['id'], $res['msg']);
                return $this->jsonResponse(0, $res['msg']);
            }

            // 处理成功拉单的订单
            $orderData = [
                'account_id' => $account['id'],
                'pay_trade_no' => $res['data']['orderId'],
                'pay_url' => $res['data']['payUrl']
            ];

            if ($order['paytype'] == 'yahu') {
                $outTradeNo = huya\Service::extractOutTradeNo($res['data']['payUrl']);
                if ($outTradeNo) {
                    $orderData['pay_trade_no'] = $outTradeNo;
                }
            }

            // 更新账号状态
            $this->updateAccountStatus($account['id']);
            $this->infoLog('拉单成功，开始更新订单', $order['out_trade_no']);

            // 使用缓存锁保护，无需数据库事务
            try {
                // 再次检查订单状态（双重检查）
                $orderModel = \app\common\model\Order::where('id', $order['id'])->find();
                // 如果订单没有支付链接，则更新订单
                if (empty($orderModel['pay_url'])) {
                    $orderModel->save($orderData);
                    $this->infoLog('更新订单成功', $order['out_trade_no']);
                } else {
                    $this->infoLog("已有支付链接，直接返回", $order['out_trade_no']);
                    // 如果订单有支付链接，则直接返回
                    $orderData['pay_url'] = $orderModel['pay_url'];
                }

                $this->infoLog('更新订单成功，返回支付链接', $order['out_trade_no']);
                return $this->jsonResponse(1, '获取支付链接成功', [
                    'url' => $orderData['pay_url']
                ]);
            } catch (\Exception $e) {
                $this->errorLog('更新订单失败，错误信息：' . $e->getMessage(), $order['out_trade_no']);
                return $this->jsonResponse(0, $e->getMessage());
            }
        } catch (\Exception $e) {
            // 捕获所有异常，确保释放锁
            $this->errorLog('处理订单异常：' . $e->getMessage(), $order['out_trade_no']);
            return $this->jsonResponse(0, '系统异常，请重试');
        } finally {
            // 确保释放锁
            CacheLock::releaseOrderLock($order['out_trade_no']);
        }
    }

    /**
     * 更新账号状态
     * @param int $accountId 账号ID
     */
    protected function updateAccountStatus($accountId)
    {
        db('account')->where('id', $accountId)->update([
            'errormsg' => '',
            'pulltime' => time(),
            'status' => 1
        ]);
    }

    /**
     * 更新账号错误信息
     * @param int $accountId 账号ID
     * @param string $errorMsg 错误信息
     */
    protected function updateAccountError($accountId, $errorMsg)
    {
        db('account')->where('id', $accountId)->update([
            'errormsg' => $errorMsg,
            'pulltime' => time()
        ]);
    }

    /**
     * 生成JSON响应
     * @param int $code 状态码
     * @param string $msg 消息
     * @param array $data 数据
     * @return string JSON字符串
     */
    protected function jsonResponse($code, $msg, $data = [])
    {
        return json_encode(array_merge([
            'code' => $code,
            'msg' => $msg
        ], $data));
    }

    /**
     * 获取可用的支付账号
     * 根据用户ID和支付类型查找可用的支付账号
     * 
     * @param int $userId 用户ID
     * @param string $paytype 支付类型
     * @return array|null 返回可用的支付账号信息
     */
    protected function getAccountCanUseList($userId)
    {
        // 获取轮询模式状态
        $pollingConfig = Config::get('polling');
        $enabled = $pollingConfig['enabled'] ?? false;
        if ($enabled) {
            // 轮询模式
            $orderStr = 'pulltime ASC';
        } else {
            // 非轮询模式,id倒序
            $orderStr = 'id DESC';
        }
        // 查询指定用户的可用支付账号
        $accountList = db('account')
            ->whereNotLike('errormsg', '%异常%')  // 排除有异常的账号
            ->whereNotLike('errormsg', '%频繁%')  // 排除频繁操作的账号
            ->whereNotLike('errormsg', '%登录%')  // 排除未登录的账号
            ->whereNotLike('errormsg', '%违规%')  // 排除未登录的账号
            ->whereNotLike('errormsg', '%风险%')  // 排除未登录的账号
            // ->where('user_id', $userId)           // 指定用户ID
            ->where('status', 1)                  // 状态正常
            ->where('paystatus', 1)              // 收款状态正常
            // ->where('pulltime', '<', time() - 300)
            ->order($orderStr)
            ->select();
        return $accountList;
    }

    /**
     * 记录日志
     * @param string $msg 日志消息
     * @param string $orderNo 订单号（可选）
     */
    protected function infoLog($msg, $orderNo = '')
    {
        $this->writeLog('info', $msg, $orderNo);
    }

    /**
     * 记录错误日志
     * @param string $msg 日志消息
     * @param string $orderNo 订单号（可选）
     */
    protected function errorLog($msg, $orderNo = '')
    {
        $this->writeLog('error', $msg, $orderNo);
    }

    /**
     * 通用日志写入
     * @param string $level 日志级别
     * @param mixed $msg 日志内容
     * @param string $orderNo 订单号
     */
    protected function writeLog($level, $msg, $orderNo = '')
    {
        // 支持数组/对象自动转json
        if (is_array($msg) || is_object($msg)) {
            $msg = json_encode($msg, JSON_UNESCAPED_UNICODE);
        }

        // 格式化日志消息
        if (!empty($orderNo)) {
            $msg = "【{$orderNo}】：{$msg}";
        }

        $log = sprintf('[%s][%s] %s', date('Y-m-d H:i:s'), $level, $msg);
        \think\Log::write($log, $level);
    }
}
