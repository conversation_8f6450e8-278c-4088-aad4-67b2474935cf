<?php

namespace huya;

use GuzzleHttp\Client;

class Service
{
    /**
     * 虎牙充值
     * @param int $amount 充值金额
     * @param string $cookie 请求cookie
     * @param string $proxy 代理
     * @return array|false 返回请求结果或false
     */
    public static function charge($amount, $cookie, $chcode, $proxy)
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $proxy = "http://" . $proxyUsername . ":" . $proxyPassword . "@" . $proxy;
        $url = "http://*************:3000/proxy.php?target_url=" . urlencode('https://charge.huya.com/order/charge');
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{"amount":' . intval($amount) . ',"chcode":"' . $chcode . '"}',
            CURLOPT_COOKIE => $cookie,
            CURLOPT_HTTPHEADER => [
                'X-Proxy: ' . $proxy,
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Content-Type: application/json',
                'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
                'cache-control: no-cache',
                'origin: https://zhifu.huya.com',
                'pragma: no-cache',
                'priority: u=1, i',
                'referer: https://zhifu.huya.com/',
                'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'sec-fetch-dest: empty',
                'sec-fetch-mode: cors',
                'sec-fetch-site: same-site',
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return false;
        }
        $response = json_decode($response, true);
        return $response;
    }

    /**
     * 获取虎牙支付页面 微信 action地址
     * @param string $url 支付页面URL
     * @param string $proxy 代理
     * @return string|false 返回action地址或false
     */
    public static function payment_wap($url, $proxy)
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $proxy = "http://" . $proxyUsername . ":" . $proxyPassword . "@" . $proxy;
        $url = "http://*************:3000/proxy.php?target_url=" . urlencode($url);
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'X-Proxy: ' . $proxy,
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept: application/json, text/plain, */*',
                'Content-Type: application/json',
                'accept-language: zh-CN,zh;q=0.9,ja;q=0.8,zh-TW;q=0.7',
                'cache-control: no-cache',
                'origin: https://zhifu.huya.com',
                'pragma: no-cache',
                'priority: u=1, i',
                'referer: https://zhifu.huya.com/',
                'sec-ch-ua: "Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "Windows"',
                'sec-fetch-dest: empty',
                'sec-fetch-mode: cors',
                'sec-fetch-site: same-site',
            ],
        ]);
        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);
        if ($err) {
            return false;
        }
        $action = self::extractPaymentAction($response);
        return $action;
    }

    public static function getQeid()
    {
        $client = new Client();
        $url = "http://*************:3000/proxy.php?target_url=https://udblgn.huya.com/qrLgn/getQrId";
        $headers = ["Cookie" => "udb_passdata=3; udb_guiddata=963c622f771b4df4a11d507bb3f00d92; udb_deviceid=w_893565676355403776"];
        $body = [
            'appId' => "5002",
            'appSign' => "",
            'authId' => "",
            "byPass" => "3",
            "context" => "WB-963c622f771b4df4a11d507bb3f00d92--",
            "data" => [
                "behavior" => "%7B%22furl%22%3A%22https%3A%2F%2Fzhifu.huya.com%2Fweb%2Fpay_hy_coin.html%22%2C%22curl%22%3A%22https%3A%2F%2Fzhifu.huya.com%2Fweb%2Fpay_hy_coin.html%22%2C%22user_action%22%3A%5B%5D%7D",
                "domainList" => "",
                "page" => "https%3A%2F%2Fzhifu.huya.com%2Fweb%2Fpay_hy_coin.html",
                "type" => ""
            ],
            "lcid" => "2052",
            "requestId" => "60642722",
            "sdid" => "",
            "uri" => "70001",
            "version" => "2.6"
        ];
        $options = [
            'headers' => $headers,
            'timeout' => 30,
            'verify' => false,
            'json' => $body
        ];
        $response = $client->request("POST", $url, $options);
        $data = json_decode($response->getBody(), true);
        return $data['data']['qrId'];
    }

    public static function tryQrLogin($qrId)
    {
        $client = new Client();
        $url = "https://udblgn.huya.com/qrLgn/tryQrLogin";
        $options = [
            'headers' => ["Cookie" => "udb_passdata=3; udb_guiddata=963c622f771b4df4a11d507bb3f00d92; udb_deviceid=w_893565676355403776"],
            'timeout' => 30,
            'verify' => false,
            'proxy' => '************************************',
            'json' => [
                'appId' => "5002",
                'appSign' => "",
                'authId' => "",
                "byPass" => "3",
                "context" => "WB-963c622f771b4df4a11d507bb3f00d92--",
                "data" => [
                    "behavior" => "%7B%22furl%22%3A%22https%3A%2F%2Fzhifu.huya.com%2F%22%2C%22curl%22%3A%22https%3A%2F%2Fzhifu.huya.com%2Fweb%2Fpay_hy_coin.html%22%2C%22user_action%22%3A%5B%5D%7D",
                    "domainList" => "",
                    "page" => "https%3A%2F%2Fzhifu.huya.com%2Fweb%2Fpay_hy_coin.html",
                    "qrId" => $qrId,
                    "remember" => "1"
                ],
                "lcid" => "2052",
                "requestId" => "60643119",
                "sdid" => "",
                "uri" => "70003",
                "version" => "2.6"
            ]
        ];
        $response = $client->request("POST", $url, $options);
        // 获取响应头中的所有cookie
        $cookies = $response->getHeader('Set-Cookie');
        if (!empty($cookies)) {
            $result = json_decode($response->getBody(), true);
            $result['cookie']['headers']['Set-Cookie'] = $cookies;
            return $result;
        }
        $result = json_decode($response->getBody(), true);
        return $result;
    }

    public static function status($orderId, $cookie)
    {
        $client = new Client();
        $url = "http://*************:3000/proxy.php?target_url=https://charge.huya.com/order/status";
        $headers = ["Cookie" => $cookie, "User-Agent" => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0"];

        $body = ['orderId' => $orderId];
        $options = [
            'headers' => $headers,
            'timeout' => 30,
            'verify' => false,
            'json' => $body
        ];
        $response = $client->request("POST", $url, $options);
        $data = json_decode($response->getBody());
        return $data;
    }

    public static function getUseableBalance($cookie)
    {
        $client = new Client();
        $url = "http://*************:3000/proxy.php?target_url=https://charge.huya.com/account/balance";
        $options = [
            'headers' => ["Cookie" => $cookie],
            'timeout' => 30,
            'verify' => false
        ];
        $response = $client->request("POST", $url, $options);
        $data = json_decode($response->getBody(), true);
        if ($data['code'] == 1002) {
            return '未登录';
        }
        return $data['data']['useableBalance'];
    }

    /**
     * 从HTML响应中提取支付表单的action地址
     * @param string $html HTML响应内容
     * @return string|false 返回action地址或false
     */
    public static function extractPaymentAction($html)
    {
        // 使用正则表达式匹配form标签中的action属性
        if (preg_match('/<form[^>]*action="([^"]*)"[^>]*>/i', $html, $matches)) {
            return $matches[1];
        }
        return false;
    }

    public static function getDeeplink($url, $proxy)
    {
        // 从Proxy类获取代理认证信息
        $proxyUsername = \Proxy::$proxyUsername;
        $proxyPassword = \Proxy::$proxyPassword;
        $proxy = "http://" . $proxyUsername . ":" . $proxyPassword . "@" . $proxy;
        $curl = curl_init();
        $url = "http://*************:3000/proxy.php?target_url=" . urlencode($url);
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'X-Proxy: ' . $proxy,
                'Referer: https://paygate.huya.com',
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);
        if ($err) {
            return false;
        }
        $deeplink = self::extractDeeplinkOrUrl($response);
        return $deeplink;
    }

    /**
     * 从HTML字符串中提取deeplink或url的值
     * @param string $html
     * @return string|false 返回deeplink或url的值，若都没有则返回false
     */
    public static function extractDeeplinkOrUrl($html)
    {
        // 先尝试提取deeplink
        if (preg_match('/deeplink\s*:\s*"([^"]+)"/', $html, $matches) && !empty($matches[1])) {
            return $matches[1];
        }
        // 再尝试提取url
        if (preg_match('/var\s+url\s*=\s*"([^"]+)"/', $html, $matches) && !empty($matches[1])) {
            return $matches[1];
        }
        return false;
    }

    /**
     * 从支付宝网关URL中提取out_trade_no参数
     * @param string $url 支付宝网关URL
     * @return string|false 返回out_trade_no值或false
     */
    public static function extractOutTradeNo($url)
    {
        // 解析URL参数
        $parsedUrl = parse_url($url);
        if (!isset($parsedUrl['query'])) {
            return false;
        }

        // 解析查询参数
        parse_str($parsedUrl['query'], $queryParams);

        // 检查是否存在biz_content参数
        if (!isset($queryParams['biz_content'])) {
            return false;
        }

        // URL解码biz_content参数
        $bizContent = urldecode($queryParams['biz_content']);

        // 解析JSON内容
        $bizData = json_decode($bizContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        // 提取out_trade_no
        if (isset($bizData['out_trade_no'])) {
            return $bizData['out_trade_no'];
        }

        return false;
    }
}
